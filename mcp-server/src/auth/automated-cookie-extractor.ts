/**
 * 🍪 Automated Cookie Extractor
 * 
 * This module automates the complete OAuth2 authentication flow to obtain
 * the test_orders-portal cookie that can be used for production API calls.
 * 
 * Flow:
 * 1. Use existing Keycloak authentication (from browser session)
 * 2. Call permission service to start login
 * 3. Follow OAuth2 redirects to get authorization code
 * 4. Exchange code for pm_user_token
 * 5. Return the cookie value for API usage
 */

import axios, { AxiosResponse } from 'axios';
import { getEnvironmentConfig } from '../utils/env.js';
import { decodeJWTUnsafe } from '../utils/jwt-utils.js';
import fs from 'fs';
import path from 'path';
import { URL, URLSearchParams } from 'url';

export interface CookieExtractionConfig {
  keycloakBaseUrl: string;
  keycloakRealm: string;
  permissionServiceBase: string;
  ordersApiBase: string;
  clientId: string;
  redirectUrl: string;
  keycloakCookies: string; // Keycloak session cookies from browser
}

export interface CookieExtractionResult {
  success: boolean;
  pmUserToken?: string;
  cookieValue?: string;
  expiresAt?: Date;
  method: string;
  error?: string;
}

/**
 * Load configuration for cookie extraction
 */
export function loadCookieExtractionConfig(): CookieExtractionConfig {
  const env = getEnvironmentConfig();
  
  return {
    keycloakBaseUrl: env.KEYCLOAK_BASE_URL || 'https://keycloak.ingka-dt.cn/auth',
    keycloakRealm: env.KEYCLOAK_REALM || 'master',
    permissionServiceBase: env.PERMISSION_SERVICE_BASE || 'https://api-dev-mpp-fe.ingka-dt.cn',
    ordersApiBase: env.ORDERS_API_BASE || 'https://fe-dev-i.ingka-dt.cn/order-web',
    clientId: 'orders-portal',
    redirectUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/user/current',
    keycloakCookies: env.KEYCLOAK_COOKIES || ''
  };
}

/**
 * Extract Keycloak cookies from browser session
 */
export function extractKeycloakCookiesFromToken(): string {
  try {
    const tokenFile = path.join(process.cwd(), 'mcp-mpc-odi.token.json');
    const tokenContent = fs.readFileSync(tokenFile, 'utf8');
    const tokenData = JSON.parse(tokenContent);
    
    if (tokenData.access_token) {
      const payload = decodeJWTUnsafe(tokenData.access_token);
      const sessionId = payload.sid;
      const userId = payload.sub;
      
      // Construct typical Keycloak cookies based on session info
      // Note: These might need to be extracted from actual browser session
      return [
        `AUTH_SESSION_ID=${sessionId}.keycloak-dev-857f5d85dc-zkl9k-27830`,
        `AUTH_SESSION_ID_LEGACY=${sessionId}.keycloak-dev-857f5d85dc-zkl9k-27830`,
        `KEYCLOAK_LOCALE=en`,
        `KEYCLOAK_SESSION="${payload.iss.split('/').pop()}/${userId}/${sessionId}"`,
        `KEYCLOAK_SESSION_LEGACY="${payload.iss.split('/').pop()}/${userId}/${sessionId}"`
      ].join('; ');
    }
  } catch (error) {
    console.error('Could not extract Keycloak cookies from token:', error);
  }
  
  return '';
}

/**
 * Step 1: Start login process with permission service
 */
export async function startPermissionServiceLogin(config: CookieExtractionConfig): Promise<{
  success: boolean;
  redirectUrl?: string;
  sessionCookie?: string;
}> {
  try {
    console.log('🔄 Step 1: Starting permission service login...');
    
    const state = generateRandomState();
    const toLoginUrl = `${config.permissionServiceBase}/prm-auth/auth/toLogin`;
    
    const params = new URLSearchParams({
      clientId: config.clientId,
      redirectUrl: config.redirectUrl,
      state: state
    });
    
    const response = await axios.get(`${toLoginUrl}?${params.toString()}`, {
      headers: {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
      },
      maxRedirects: 0,
      validateStatus: (status) => status === 302 || status === 200
    });
    
    if (response.status === 302 && response.headers.location) {
      console.log('✅ Permission service redirected to Keycloak');
      
      // Extract session cookie
      const setCookieHeaders = response.headers['set-cookie'] || [];
      const sessionCookie = setCookieHeaders
        .find(cookie => cookie.startsWith('JSESSIONID='))
        ?.split(';')[0];
      
      return {
        success: true,
        redirectUrl: response.headers.location,
        sessionCookie: sessionCookie || ''
      };
    }
    
    return { success: false };
    
  } catch (error) {
    console.error('Permission service login failed:', error);
    return { success: false };
  }
}

/**
 * Step 2: Follow Keycloak authentication with existing session
 */
export async function followKeycloakAuth(
  authUrl: string, 
  keycloakCookies: string
): Promise<{
  success: boolean;
  loginCompleteUrl?: string;
}> {
  try {
    console.log('🔄 Step 2: Following Keycloak authentication...');
    
    const response = await axios.get(authUrl, {
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'Cache-Control': 'no-cache',
        'Cookie': keycloakCookies,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
      },
      maxRedirects: 0,
      validateStatus: (status) => status === 302 || status === 200
    });
    
    if (response.status === 302 && response.headers.location) {
      console.log('✅ Keycloak authenticated, redirecting to loginComplete');
      return {
        success: true,
        loginCompleteUrl: response.headers.location
      };
    }
    
    return { success: false };
    
  } catch (error) {
    console.error('Keycloak authentication failed:', error);
    return { success: false };
  }
}

/**
 * Step 3: Complete login and get pm_user_token
 */
export async function completeLogin(
  loginCompleteUrl: string,
  sessionCookie: string
): Promise<{
  success: boolean;
  pmUserToken?: string;
  finalRedirectUrl?: string;
}> {
  try {
    console.log('🔄 Step 3: Completing login to get pm_user_token...');
    
    const response = await axios.get(loginCompleteUrl, {
      headers: {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'Cookie': sessionCookie,
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
      },
      maxRedirects: 0,
      validateStatus: (status) => status === 302 || status === 200
    });
    
    if (response.status === 302 && response.headers.location) {
      const redirectUrl = response.headers.location;
      console.log('✅ Login completed, got redirect with pm_user_token');
      
      // Extract pm_user_token from redirect URL
      const url = new URL(redirectUrl);
      const pmUserToken = url.searchParams.get('pm_user_token');
      
      if (pmUserToken) {
        return {
          success: true,
          pmUserToken,
          finalRedirectUrl: redirectUrl
        };
      }
    }
    
    return { success: false };
    
  } catch (error) {
    console.error('Login completion failed:', error);
    return { success: false };
  }
}

/**
 * Step 4: Follow final redirect to set cookie
 */
export async function followFinalRedirect(
  finalRedirectUrl: string
): Promise<{
  success: boolean;
  cookieValue?: string;
}> {
  try {
    console.log('🔄 Step 4: Following final redirect to set cookie...');
    
    const response = await axios.get(finalRedirectUrl, {
      headers: {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'cache-control': 'no-cache',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
      },
      maxRedirects: 0,
      validateStatus: () => true
    });
    
    // Extract test_orders-portal cookie from Set-Cookie headers
    const setCookieHeaders = response.headers['set-cookie'] || [];
    const ordersCookie = setCookieHeaders
      .find(cookie => cookie.includes('test_orders-portal='))
      ?.split(';')[0]
      ?.split('=')[1];
    
    if (ordersCookie) {
      console.log('✅ Successfully extracted test_orders-portal cookie');
      return {
        success: true,
        cookieValue: ordersCookie
      };
    }
    
    return { success: false };
    
  } catch (error) {
    console.error('Final redirect failed:', error);
    return { success: false };
  }
}

/**
 * Generate random state for OAuth2
 */
function generateRandomState(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

/**
 * Test the extracted cookie with orders API
 */
export async function testExtractedCookie(cookieValue: string): Promise<boolean> {
  try {
    console.log('🧪 Testing extracted cookie with orders API...');
    
    const testUrl = 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search';
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };
    
    const response = await axios.post(testUrl, requestData, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'content-type': 'application/json',
        'Cookie': `test_orders-portal=${cookieValue}`,
        'origin': 'https://admin.ingka-dt.cn',
        'referer': 'https://admin.ingka-dt.cn/',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    const isWorking = response.status === 200 && 
                     response.data && 
                     response.data.code !== 401 && 
                     response.data.message !== 'to login';
    
    if (isWorking) {
      console.log('✅ Cookie test successful - API returned data');
    } else {
      console.log('❌ Cookie test failed - API rejected request');
    }
    
    return isWorking;
    
  } catch (error) {
    console.error('Cookie test failed:', error);
    return false;
  }
}

/**
 * Main function: Automated cookie extraction
 */
export async function extractCookieAutomatically(): Promise<CookieExtractionResult> {
  console.log('🍪 ==================== AUTOMATED COOKIE EXTRACTION ====================');
  console.log('🎯 Purpose: Automatically obtain test_orders-portal cookie for production use\n');

  const config = loadCookieExtractionConfig();

  // Try to get Keycloak cookies from token or environment
  let keycloakCookies = config.keycloakCookies;
  if (!keycloakCookies) {
    keycloakCookies = extractKeycloakCookiesFromToken();
  }

  console.log('📋 Configuration:');
  console.log(`   Permission Service: ${config.permissionServiceBase}`);
  console.log(`   Orders API: ${config.ordersApiBase}`);
  console.log(`   Client ID: ${config.clientId}`);
  console.log(`   Keycloak Cookies: ${keycloakCookies ? 'Available ✅' : 'Missing ❌'}`);
  console.log('');

  if (!keycloakCookies) {
    return {
      success: false,
      method: 'Automated Extraction',
      error: 'Missing Keycloak session cookies. Please set KEYCLOAK_COOKIES environment variable or ensure token file contains valid session info.'
    };
  }

  try {
    // Step 1: Start permission service login
    const loginStart = await startPermissionServiceLogin(config);
    if (!loginStart.success || !loginStart.redirectUrl || !loginStart.sessionCookie) {
      return {
        success: false,
        method: 'Automated Extraction',
        error: 'Failed to start permission service login'
      };
    }

    // Step 2: Follow Keycloak authentication
    const keycloakAuth = await followKeycloakAuth(loginStart.redirectUrl, keycloakCookies);
    if (!keycloakAuth.success || !keycloakAuth.loginCompleteUrl) {
      return {
        success: false,
        method: 'Automated Extraction',
        error: 'Failed to authenticate with Keycloak'
      };
    }

    // Step 3: Complete login and get pm_user_token
    const loginComplete = await completeLogin(keycloakAuth.loginCompleteUrl, loginStart.sessionCookie);
    if (!loginComplete.success || !loginComplete.pmUserToken || !loginComplete.finalRedirectUrl) {
      return {
        success: false,
        method: 'Automated Extraction',
        error: 'Failed to complete login and get pm_user_token'
      };
    }

    // Step 4: Follow final redirect to get cookie
    const finalRedirect = await followFinalRedirect(loginComplete.finalRedirectUrl);
    if (!finalRedirect.success || !finalRedirect.cookieValue) {
      // If final redirect fails, we still have the pm_user_token
      console.log('⚠️ Final redirect failed, but we have pm_user_token');
      const cookieValue = loginComplete.pmUserToken;

      // Test the pm_user_token directly
      const isWorking = await testExtractedCookie(cookieValue);

      return {
        success: isWorking,
        pmUserToken: cookieValue,
        cookieValue: cookieValue,
        method: 'Automated Extraction (pm_user_token)',
        error: isWorking ? undefined : 'pm_user_token not accepted by API'
      };
    }

    // Step 5: Test the extracted cookie
    const isWorking = await testExtractedCookie(finalRedirect.cookieValue);

    if (isWorking) {
      console.log('🎉 SUCCESS! Automated cookie extraction completed successfully!');

      // Decode token to get expiration info
      let expiresAt: Date | undefined;
      try {
        const tokenString = Buffer.from(finalRedirect.cookieValue, 'base64').toString();
        const parts = tokenString.split('@');
        if (parts.length === 2) {
          const rightPart = parts[1];
          const rightParts = rightPart.split('.');
          if (rightParts.length >= 3) {
            const loginTime = parseInt(rightParts[0]);
            const sessionTimeout = parseInt(rightParts[rightParts.length - 1]);
            expiresAt = new Date(loginTime + (sessionTimeout * 1000));
          }
        }
      } catch (error) {
        // Ignore decode errors
      }

      return {
        success: true,
        pmUserToken: loginComplete.pmUserToken,
        cookieValue: finalRedirect.cookieValue,
        method: 'Automated Extraction',
        expiresAt
      };
    } else {
      return {
        success: false,
        method: 'Automated Extraction',
        error: 'Cookie extracted but not accepted by orders API'
      };
    }

  } catch (error) {
    console.error('Automated cookie extraction failed:', error);
    return {
      success: false,
      method: 'Automated Extraction',
      error: `Extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Get session cookie for API calls using automated extraction
 */
export async function getAutomatedSessionCookie(): Promise<string | null> {
  const result = await extractCookieAutomatically();

  if (result.success && result.cookieValue) {
    return `test_orders-portal=${result.cookieValue}`;
  }

  return null;
}

/**
 * Integration with existing production auth solution
 */
export async function getProductionCookie(): Promise<{
  success: boolean;
  cookie?: string;
  method?: string;
  expiresAt?: Date;
  error?: string;
}> {
  // Try automated extraction first
  const automatedResult = await extractCookieAutomatically();

  if (automatedResult.success) {
    return {
      success: true,
      cookie: `test_orders-portal=${automatedResult.cookieValue}`,
      method: automatedResult.method,
      expiresAt: automatedResult.expiresAt
    };
  }

  // Fallback to existing production auth solution
  const { getSessionCookie } = await import('./production-auth-solution.js');
  const fallbackCookie = await getSessionCookie();

  if (fallbackCookie) {
    return {
      success: true,
      cookie: fallbackCookie,
      method: 'Fallback Production Auth'
    };
  }

  return {
    success: false,
    error: 'All authentication methods failed'
  };
}
