/**
 * 🔐 Per-Request Authentication Middleware
 * 
 * Implements MCP specification requirement:
 * "authorization MUST be included in every HTTP request from client to server,
 * even if they are part of the same logical session."
 */

import { Request, Response, NextFunction } from 'express';
import { ProxyOAuthServerProvider } from '@modelcontextprotocol/sdk/server/auth/providers/proxyProvider.js';
import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';
import { getEnvironmentConfig } from '../utils/env.js';

export interface PerRequestAuthConfig {
  enabled: boolean;
  skipPaths?: string[]; // Paths to skip validation (e.g., health, metadata)
  cacheEnabled: boolean;
  cacheMaxAge: number; // seconds
  logValidation: boolean;
}

export interface AuthValidationResult {
  success: boolean;
  authInfo?: AuthInfo;
  error?: string;
  cached?: boolean;
  duration?: number;
}

/**
 * Simple in-memory token cache with expiration
 */
class TokenValidationCache {
  private cache = new Map<string, { authInfo: AuthInfo; expiresAt: number; validatedAt: number }>();
  private maxAge: number;

  constructor(maxAgeSeconds: number = 300) { // 5 minutes default
    this.maxAge = maxAgeSeconds * 1000;
  }

  get(token: string): AuthInfo | null {
    const entry = this.cache.get(token);
    if (!entry) return null;

    const now = Date.now();
    
    // Check cache expiration
    if (now > entry.expiresAt) {
      this.cache.delete(token);
      return null;
    }

    // Check token expiration (if available)
    if (entry.authInfo.expiresAt && now > (entry.authInfo.expiresAt * 1000)) {
      this.cache.delete(token);
      return null;
    }

    return entry.authInfo;
  }

  set(token: string, authInfo: AuthInfo): void {
    const now = Date.now();
    const expiresAt = now + this.maxAge;
    
    this.cache.set(token, {
      authInfo,
      expiresAt,
      validatedAt: now
    });
  }

  clear(): void {
    this.cache.clear();
  }

  getStats() {
    const now = Date.now();
    const entries = Array.from(this.cache.values());
    const validEntries = entries.filter(e => now <= e.expiresAt);
    
    return {
      totalEntries: this.cache.size,
      validEntries: validEntries.length,
      expiredEntries: entries.length - validEntries.length,
      oldestEntry: entries.length > 0 ? Math.min(...entries.map(e => e.validatedAt)) : null,
      newestEntry: entries.length > 0 ? Math.max(...entries.map(e => e.validatedAt)) : null
    };
  }
}

// Global token cache instance
const globalTokenCache = new TokenValidationCache();

/**
 * Load per-request authentication configuration
 */
export function loadPerRequestAuthConfig(): PerRequestAuthConfig {
  // Use environment config to ensure .env is loaded
  const env = getEnvironmentConfig();

  return {
    enabled: process.env.PER_REQUEST_AUTH_ENABLED === 'true',
    skipPaths: ['/health', '/.well-known', '/authorize', '/token', '/revoke', '/register'],
    cacheEnabled: process.env.PER_REQUEST_AUTH_CACHE_ENABLED !== 'false', // enabled by default
    cacheMaxAge: parseInt(process.env.PER_REQUEST_AUTH_CACHE_MAX_AGE || '300'), // 5 minutes
    logValidation: process.env.PER_REQUEST_AUTH_LOG_VALIDATION === 'true'
  };
}

/**
 * Validate OAuth2 Bearer token for a single request
 */
export async function validateRequestToken(
  req: Request,
  oauth2Provider?: ProxyOAuthServerProvider
): Promise<AuthValidationResult> {
  const startTime = Date.now();
  const config = loadPerRequestAuthConfig();

  try {
    // Extract Bearer token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        success: false,
        error: 'Missing or invalid Authorization header',
        duration: Date.now() - startTime
      };
    }

    const token = authHeader.substring(7);
    
    // Check cache first (if enabled)
    if (config.cacheEnabled) {
      const cachedAuth = globalTokenCache.get(token);
      if (cachedAuth) {
        if (config.logValidation) {
          console.log('🎯 [PerRequestAuth] Cache hit for token validation');
        }
        return {
          success: true,
          authInfo: cachedAuth,
          cached: true,
          duration: Date.now() - startTime
        };
      }
    }

    // Validate with OAuth2 provider
    if (!oauth2Provider) {
      return {
        success: false,
        error: 'OAuth2 provider not available',
        duration: Date.now() - startTime
      };
    }

    const authInfo = await oauth2Provider.verifyAccessToken(token);
    
    // Cache the result (if enabled)
    if (config.cacheEnabled) {
      globalTokenCache.set(token, authInfo);
    }

    if (config.logValidation) {
      console.log('✅ [PerRequestAuth] Token validation successful:', {
        clientId: authInfo.clientId,
        scopes: authInfo.scopes?.slice(0, 5),
        cached: false,
        duration: Date.now() - startTime
      });
    }

    return {
      success: true,
      authInfo,
      cached: false,
      duration: Date.now() - startTime
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    
    if (config.logValidation) {
      console.log('❌ [PerRequestAuth] Token validation failed:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        duration
      });
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Token validation failed',
      duration
    };
  }
}

/**
 * Express middleware for per-request OAuth2 token validation
 */
export function createPerRequestAuthMiddleware(
  oauth2Provider?: ProxyOAuthServerProvider
) {
  const config = loadPerRequestAuthConfig();

  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip validation if disabled
    if (!config.enabled) {
      return next();
    }

    // Skip validation for certain paths
    if (config.skipPaths?.some(path => req.path.startsWith(path))) {
      return next();
    }

    // Skip validation for non-MCP requests (only validate /mcp endpoint)
    if (!req.path.startsWith('/mcp')) {
      return next();
    }

    const validationResult = await validateRequestToken(req, oauth2Provider);

    if (!validationResult.success) {
      // Set proper WWW-Authenticate header
      res.set('WWW-Authenticate', `Bearer realm="MCP Server", error="invalid_token", error_description="${validationResult.error}"`);
      
      return res.status(401).json({
        error: 'invalid_token',
        error_description: validationResult.error,
        error_uri: 'https://tools.ietf.org/html/rfc6750#section-3.1'
      });
    }

    // Attach auth info to request for downstream use
    (req as any).auth = validationResult.authInfo;
    (req as any).authValidation = {
      cached: validationResult.cached,
      duration: validationResult.duration
    };

    next();
  };
}

/**
 * Get token cache statistics
 */
export function getTokenCacheStats() {
  return globalTokenCache.getStats();
}

/**
 * Clear token cache (useful for testing or security)
 */
export function clearTokenCache() {
  globalTokenCache.clear();
}

/**
 * Middleware to add authentication metrics to response headers
 */
export function createAuthMetricsMiddleware() {
  return (req: Request, res: Response, next: NextFunction) => {
    const authValidation = (req as any).authValidation;
    
    if (authValidation) {
      res.set('X-Auth-Cached', authValidation.cached ? 'true' : 'false');
      res.set('X-Auth-Duration', `${authValidation.duration}ms`);
    }

    next();
  };
}
