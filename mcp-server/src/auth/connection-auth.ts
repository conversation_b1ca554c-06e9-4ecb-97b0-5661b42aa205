/**
 * 🔐 Connection Authentication Validator
 * 
 * This module validates authentication during MCP connection establishment.
 * It ensures that clients are properly authenticated before allowing MCP operations.
 */

import { Request } from 'express';
import { getEnvironmentConfig } from '../utils/env.js';
import { ProxyOAuthServerProvider } from '@modelcontextprotocol/sdk/server/auth/providers/proxyProvider.js';

export interface ConnectionAuthResult {
  success: boolean;
  error?: string;
  userInfo?: any;
  scopes?: string[];
  clientId?: string;
}

export interface ConnectionAuthConfig {
  enabled: boolean;
  strict: boolean;
  testApi: boolean;
  skipScopeValidation: boolean;
  requiredScopes?: string[];
  allowedClients?: string[];
}

/**
 * Load connection authentication configuration
 */
export function loadConnectionAuthConfig(): ConnectionAuthConfig {
  const env = getEnvironmentConfig();
  
  return {
    enabled: env.CONNECTION_AUTH_ENABLED,
    strict: env.CONNECTION_AUTH_STRICT,
    testApi: env.CONNECTION_AUTH_TEST_API,
    skipScopeValidation: env.CONNECTION_AUTH_SKIP_SCOPE_VALIDATION,
    requiredScopes: ['profile'], // Minimum required scopes (matches OAUTH2_SCOPES)
    allowedClients: ['permission-service', 'mcp-mcp-odi', 'mcp-inspector'] // Allowed OAuth2 clients
  };
}

/**
 * Validate OAuth2 token during connection
 */
export async function validateOAuth2Connection(
  req: Request,
  oauth2Provider?: ProxyOAuthServerProvider
): Promise<ConnectionAuthResult> {
  const config = loadConnectionAuthConfig();
  
  if (!config.enabled) {
    return { success: true }; // Authentication disabled
  }
  
  if (!oauth2Provider) {
    return {
      success: false,
      error: 'OAuth2 provider not available'
    };
  }
  
  try {
    // Extract Bearer token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        success: false,
        error: 'Missing or invalid Authorization header'
      };
    }
    
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Validate token with OAuth2 provider
    const tokenInfo = await oauth2Provider.verifyAccessToken(token);
    
    if (!tokenInfo) {
      return {
        success: false,
        error: 'Invalid or expired token'
      };
    }
    
    // Extract user information from AuthInfo structure
    const userInfo = tokenInfo.extra?.userInfo || tokenInfo.extra?.introspectionData || {};
    const scopes = tokenInfo.scopes || [];
    const clientId = tokenInfo.clientId || (userInfo as any)?.azp || (userInfo as any)?.client_id;

    console.log('🔐 [ConnectionAuth] Token validation successful:', {
      clientId,
      subject: (userInfo as any)?.sub,
      scopes: scopes.slice(0, 10), // Log more scopes for debugging
      scopeCount: scopes.length,
      expiresAt: (userInfo as any)?.exp ? new Date((userInfo as any).exp * 1000).toISOString() : 'unknown'
    });

    // Validate required scopes (unless skipped)
    if (!config.skipScopeValidation && config.requiredScopes && config.requiredScopes.length > 0) {
      const hasRequiredScopes = config.requiredScopes.every(scope =>
        scopes.includes(scope)
      );

      if (!hasRequiredScopes) {
        const missingScopes = config.requiredScopes.filter(scope => !scopes.includes(scope));
        console.log('❌ [ConnectionAuth] Missing required scopes:', {
          required: config.requiredScopes,
          provided: scopes,
          missing: missingScopes
        });

        return {
          success: false,
          error: `Missing required scopes. Required: [${config.requiredScopes.join(', ')}], Provided: [${scopes.join(', ')}], Missing: [${missingScopes.join(', ')}]`
        };
      }
    } else if (config.skipScopeValidation) {
      console.log('⚠️ [ConnectionAuth] Scope validation skipped (development mode)');
    }
    
    // Validate allowed clients
    if (config.allowedClients && config.allowedClients.length > 0) {
      if (!clientId || !config.allowedClients.includes(clientId)) {
        return {
          success: false,
          error: `Client '${clientId}' not allowed. Allowed clients: ${config.allowedClients.join(', ')}`
        };
      }
    }
    
    return {
      success: true,
      userInfo,
      scopes,
      clientId
    };
    
  } catch (error) {
    console.error('🔐 [ConnectionAuth] Token validation failed:', error);
    return {
      success: false,
      error: `Token validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Validate legacy cookie authentication during connection
 */
export async function validateCookieConnection(req: Request): Promise<ConnectionAuthResult> {
  const config = loadConnectionAuthConfig();
  
  if (!config.enabled) {
    return { success: true }; // Authentication disabled
  }
  
  try {
    // Check for authentication cookies
    const cookies = req.headers.cookie;
    if (!cookies) {
      return {
        success: false,
        error: 'No authentication cookies provided'
      };
    }
    
    // Look for orders portal cookie
    const ordersCookieMatch = cookies.match(/test_orders-portal=([^;]+)/);
    if (!ordersCookieMatch) {
      return {
        success: false,
        error: 'Missing test_orders-portal cookie'
      };
    }
    
    const pmUserToken = ordersCookieMatch[1];
    
    // Validate token format (Base64 encoded)
    try {
      const tokenString = Buffer.from(pmUserToken, 'base64').toString();
      const parts = tokenString.split('@');
      
      if (parts.length !== 2) {
        throw new Error('Invalid token format');
      }
      
      const [ssoUUID, rightPart] = parts;
      const rightParts = rightPart.split('.');
      
      if (rightParts.length < 3) {
        throw new Error('Invalid token structure');
      }
      
      const loginTime = parseInt(rightParts[0]);
      const userId = rightParts[1];
      const sessionTimeout = parseInt(rightParts[rightParts.length - 1]);
      
      // Check if token is expired
      const expiresAt = new Date(loginTime + (sessionTimeout * 1000));
      const now = new Date();
      
      if (now > expiresAt) {
        return {
          success: false,
          error: `Token expired at ${expiresAt.toISOString()}`
        };
      }
      
      console.log('🍪 [ConnectionAuth] Cookie validation successful:', {
        userId,
        ssoUUID: ssoUUID.substring(0, 8) + '...',
        loginTime: new Date(loginTime).toISOString(),
        expiresAt: expiresAt.toISOString()
      });
      
      // Test API call if enabled
      if (config.testApi) {
        const apiTestResult = await testApiConnection(pmUserToken);
        if (!apiTestResult.success) {
          return apiTestResult;
        }
      }
      
      return {
        success: true,
        userInfo: {
          userId,
          ssoUUID,
          loginTime: new Date(loginTime),
          expiresAt
        },
        clientId: 'cookie-auth'
      };
      
    } catch (error) {
      return {
        success: false,
        error: `Invalid token format: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
    
  } catch (error) {
    console.error('🍪 [ConnectionAuth] Cookie validation failed:', error);
    return {
      success: false,
      error: `Cookie validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Test API connection with authentication token
 */
export async function testApiConnection(pmUserToken: string): Promise<ConnectionAuthResult> {
  try {
    const env = getEnvironmentConfig();
    const testUrl = `${env.VITE_API_HOST_KONG}/orders/search`;
    
    const requestData = {
      storeIds: [],
      baseSize: 1,
      page: 1,
      size: 1,
      timestamp: Date.now()
    };
    
    const response = await fetch(testUrl, {
      method: 'POST',
      headers: {
        'accept': 'application/json, text/plain, */*',
        'content-type': 'application/json',
        'Cookie': `test_orders-portal=${pmUserToken}`,
        'origin': 'https://admin.ingka-dt.cn',
        'referer': 'https://admin.ingka-dt.cn/',
        'user-agent': 'MCP-Server/1.0.0',
        'x-custom-referrer': env.X_CUSTOM_REFERRER
      },
      body: JSON.stringify(requestData)
    });
    
    const isWorking = response.status === 200;
    
    if (isWorking) {
      console.log('🧪 [ConnectionAuth] API test successful');
      return { success: true };
    } else {
      const errorText = await response.text();
      console.log('🧪 [ConnectionAuth] API test failed:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText.substring(0, 200)
      });
      
      return {
        success: false,
        error: `API test failed: ${response.status} ${response.statusText}`
      };
    }
    
  } catch (error) {
    console.error('🧪 [ConnectionAuth] API test error:', error);
    return {
      success: false,
      error: `API test error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Main connection authentication validator
 */
export async function validateConnection(
  req: Request,
  oauth2Provider?: ProxyOAuthServerProvider
): Promise<ConnectionAuthResult> {
  const config = loadConnectionAuthConfig();
  
  if (!config.enabled) {
    console.log('🔐 [ConnectionAuth] Authentication disabled, allowing connection');
    return { success: true };
  }
  
  console.log('🔐 [ConnectionAuth] Validating connection authentication...');
  
  // Try OAuth2 authentication first
  if (oauth2Provider && req.headers.authorization) {
    console.log('🔐 [ConnectionAuth] Attempting OAuth2 validation...');
    const oauth2Result = await validateOAuth2Connection(req, oauth2Provider);
    
    if (oauth2Result.success) {
      console.log('✅ [ConnectionAuth] OAuth2 authentication successful');
      return oauth2Result;
    } else if (config.strict) {
      console.log('❌ [ConnectionAuth] OAuth2 authentication failed (strict mode):', oauth2Result.error);
      return oauth2Result;
    } else {
      console.log('⚠️ [ConnectionAuth] OAuth2 authentication failed, trying cookie fallback:', oauth2Result.error);
    }
  }
  
  // Try cookie authentication as fallback
  if (req.headers.cookie) {
    console.log('🍪 [ConnectionAuth] Attempting cookie validation...');
    const cookieResult = await validateCookieConnection(req);
    
    if (cookieResult.success) {
      console.log('✅ [ConnectionAuth] Cookie authentication successful');
      return cookieResult;
    } else {
      console.log('❌ [ConnectionAuth] Cookie authentication failed:', cookieResult.error);
      return cookieResult;
    }
  }
  
  // No authentication provided
  const error = 'No valid authentication provided. Please provide either OAuth2 Bearer token or authentication cookies.';
  console.log('❌ [ConnectionAuth] No authentication provided');
  
  return {
    success: false,
    error
  };
}
