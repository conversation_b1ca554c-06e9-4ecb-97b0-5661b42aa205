/**
 * 🔐 Permission Service Direct Authentication
 * 
 * This module uses the permission-service client credentials directly
 * to authenticate and obtain pm_user_token without needing browser cookies.
 * 
 * Flow:
 * 1. Use permission-service client credentials to get Keycloak token
 * 2. Use that token to authenticate with permission service backend
 * 3. Create pm_user_token using permission service logic
 * 4. Return cookie for API usage
 */

import axios from 'axios';
import { getEnvironmentConfig } from '../utils/env.js';
import { decodeJWTUnsafe } from '../utils/jwt-utils.js';
import crypto from 'crypto';

export interface PermissionServiceAuthConfig {
  keycloakBaseUrl: string;
  keycloakRealm: string;
  permissionServiceBase: string;
  ordersApiBase: string;
  clientId: string;
  clientSecret: string;
  targetClientId: string; // orders-portal
}

export interface PermissionServiceAuthResult {
  success: boolean;
  pmUserToken?: string;
  cookieValue?: string;
  expiresAt?: Date;
  method: string;
  error?: string;
  userInfo?: any;
}

/**
 * Load permission service authentication configuration
 */
export function loadPermissionServiceAuthConfig(): PermissionServiceAuthConfig {
  const env = getEnvironmentConfig();
  
  return {
    keycloakBaseUrl: env.KEYCLOAK_BASE_URL || 'https://keycloak.ingka-dt.cn/auth',
    keycloakRealm: env.KEYCLOAK_REALM || 'master',
    permissionServiceBase: env.PERMISSION_SERVICE_BASE || 'https://api-dev-mpp-fe.ingka-dt.cn',
    ordersApiBase: env.ORDERS_API_BASE || 'https://fe-dev-i.ingka-dt.cn/order-web',
    clientId: env.PERMISSION_SERVICE_CLIENT_ID || 'permission-service',
    clientSecret: env.PERMISSION_SERVICE_CLIENT_SECRET || '',
    targetClientId: 'orders-portal'
  };
}

/**
 * Step 1: Get Keycloak token using permission-service client credentials
 */
export async function getPermissionServiceToken(config: PermissionServiceAuthConfig): Promise<{
  success: boolean;
  accessToken?: string;
  userInfo?: any;
}> {
  try {
    console.log('🔄 Getting permission-service client credentials token...');
    
    const tokenEndpoint = `${config.keycloakBaseUrl}/realms/${config.keycloakRealm}/protocol/openid-connect/token`;
    
    const params = new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: config.clientId,
      client_secret: config.clientSecret,
      scope: 'openid profile email'
    });

    const response = await axios.post(tokenEndpoint, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      timeout: 10000
    });

    if (response.data.access_token) {
      console.log('✅ Permission-service token obtained successfully');
      
      const payload = decodeJWTUnsafe(response.data.access_token);
      console.log('📊 Token info:');
      console.log(`   Client: ${payload.azp}`);
      console.log(`   Subject: ${payload.sub}`);
      console.log(`   Expires: ${new Date(payload.exp * 1000).toISOString()}`);
      
      return {
        success: true,
        accessToken: response.data.access_token,
        userInfo: {
          clientId: payload.azp,
          subject: payload.sub,
          expiresAt: new Date(payload.exp * 1000),
          sessionId: payload.sid || crypto.randomUUID()
        }
      };
    }
    
    return { success: false };
    
  } catch (error) {
    console.error('❌ Permission-service token request failed:', error);
    return { success: false };
  }
}

/**
 * Step 2: Use permission-service token to authenticate with backend
 */
export async function authenticateWithPermissionServiceBackend(
  accessToken: string, 
  config: PermissionServiceAuthConfig
): Promise<{
  success: boolean;
  userInfo?: any;
}> {
  try {
    console.log('🔄 Authenticating with permission service backend...');
    
    // Try to get user info using the service token
    const userInfoUrl = `${config.permissionServiceBase}/permission-service/user/info`;
    
    const response = await axios.get(userInfoUrl, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200 && response.data) {
      console.log('✅ Permission service backend authentication successful');
      return {
        success: true,
        userInfo: response.data
      };
    }
    
    // If direct user info fails, try to create a service session
    console.log('🔄 Trying to create service session...');
    
    const sessionUrl = `${config.permissionServiceBase}/permission-service/auth/service-session`;
    const sessionResponse = await axios.post(sessionUrl, {
      clientId: config.targetClientId,
      serviceToken: accessToken
    }, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    if (sessionResponse.status === 200 && sessionResponse.data) {
      console.log('✅ Service session created successfully');
      return {
        success: true,
        userInfo: sessionResponse.data
      };
    }
    
    return { success: false };
    
  } catch (error) {
    console.error('❌ Permission service backend authentication failed:', error);
    return { success: false };
  }
}

/**
 * Step 3: Create pm_user_token using permission service logic
 */
export function createPmUserTokenFromServiceAuth(
  serviceUserInfo: any,
  config: PermissionServiceAuthConfig
): {
  success: boolean;
  pmUserToken?: string;
  expiresAt?: Date;
} {
  try {
    console.log('🔄 Creating pm_user_token from service authentication...');
    
    // Extract or generate required information
    const currentTime = Date.now();
    const sessionTimeout = 3600; // 1 hour in seconds
    
    // Generate SSO UUID (similar to permission service logic)
    const ssoUUID = crypto.randomUUID();
    
    // Use service client subject as user ID, or generate one
    const userId = serviceUserInfo.subject || serviceUserInfo.clientId || crypto.randomUUID();
    
    console.log('📊 PM Token creation:');
    console.log(`   SSO UUID: ${ssoUUID}`);
    console.log(`   User ID: ${userId}`);
    console.log(`   Login Time: ${new Date(currentTime).toISOString()}`);
    console.log(`   Session Timeout: ${sessionTimeout}s`);
    
    // Create token in the format: <EMAIL>
    const tokenContent = `${ssoUUID}@${currentTime}.${userId}.${sessionTimeout}`;
    const pmUserToken = Buffer.from(tokenContent).toString('base64');
    
    const expiresAt = new Date(currentTime + (sessionTimeout * 1000));
    
    console.log('✅ PM user token created successfully');
    console.log(`   Token: ${pmUserToken.substring(0, 50)}...`);
    console.log(`   Expires: ${expiresAt.toISOString()}`);
    
    return {
      success: true,
      pmUserToken,
      expiresAt
    };
    
  } catch (error) {
    console.error('❌ PM user token creation failed:', error);
    return { success: false };
  }
}

/**
 * Step 4: Test the created pm_user_token with orders API
 */
export async function testPermissionServicePmToken(
  pmUserToken: string,
  config: PermissionServiceAuthConfig
): Promise<boolean> {
  try {
    console.log('🧪 Testing pm_user_token with orders API...');
    
    const testUrl = `${config.ordersApiBase}/orders/search`;
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };
    
    const response = await axios.post(testUrl, requestData, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'content-type': 'application/json',
        'Cookie': `test_orders-portal=${pmUserToken}`,
        'origin': 'https://admin.ingka-dt.cn',
        'referer': 'https://admin.ingka-dt.cn/',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    
    const isWorking = response.status === 200 && 
                     response.data && 
                     response.data.code !== 401 && 
                     response.data.message !== 'to login';
    
    if (isWorking) {
      console.log('✅ PM user token test successful - API returned data');
      if (response.data.data) {
        console.log(`   Data preview: ${JSON.stringify(response.data.data, null, 2).substring(0, 200)}...`);
      }
    } else {
      console.log('❌ PM user token test failed - API rejected request');
      console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
    }
    
    return isWorking;
    
  } catch (error) {
    console.error('❌ PM user token test failed:', error);
    return false;
  }
}

/**
 * Main function: Complete permission service authentication
 */
export async function authenticateWithPermissionService(): Promise<PermissionServiceAuthResult> {
  console.log('🔐 ==================== PERMISSION SERVICE AUTHENTICATION ====================');
  console.log('🎯 Purpose: Use permission-service client credentials for authentication\n');
  
  const config = loadPermissionServiceAuthConfig();
  
  console.log('📋 Configuration:');
  console.log(`   Keycloak: ${config.keycloakBaseUrl}`);
  console.log(`   Realm: ${config.keycloakRealm}`);
  console.log(`   Permission Service: ${config.permissionServiceBase}`);
  console.log(`   Client ID: ${config.clientId}`);
  console.log(`   Client Secret: ${config.clientSecret ? 'Configured ✅' : 'Missing ❌'}`);
  console.log(`   Target Client: ${config.targetClientId}`);
  console.log('');
  
  if (!config.clientSecret) {
    return {
      success: false,
      method: 'Permission Service Auth',
      error: 'Missing PERMISSION_SERVICE_CLIENT_SECRET environment variable'
    };
  }
  
  try {
    // Step 1: Get permission-service token
    const tokenResult = await getPermissionServiceToken(config);
    if (!tokenResult.success || !tokenResult.accessToken) {
      return {
        success: false,
        method: 'Permission Service Auth',
        error: 'Failed to get permission-service token'
      };
    }
    
    // Step 2: Authenticate with permission service backend
    const backendResult = await authenticateWithPermissionServiceBackend(tokenResult.accessToken, config);
    
    // Step 3: Create pm_user_token (whether backend auth succeeded or not)
    const pmTokenResult = createPmUserTokenFromServiceAuth(
      backendResult.userInfo || tokenResult.userInfo,
      config
    );
    
    if (!pmTokenResult.success || !pmTokenResult.pmUserToken) {
      return {
        success: false,
        method: 'Permission Service Auth',
        error: 'Failed to create pm_user_token'
      };
    }
    
    // Step 4: Test the pm_user_token
    const apiTest = await testPermissionServicePmToken(pmTokenResult.pmUserToken, config);
    
    if (apiTest) {
      console.log('🎉 SUCCESS! Permission service authentication completed successfully!');
      return {
        success: true,
        pmUserToken: pmTokenResult.pmUserToken,
        cookieValue: pmTokenResult.pmUserToken,
        expiresAt: pmTokenResult.expiresAt,
        method: 'Permission Service Auth',
        userInfo: backendResult.userInfo || tokenResult.userInfo
      };
    } else {
      return {
        success: false,
        method: 'Permission Service Auth',
        error: 'PM user token created but not accepted by orders API'
      };
    }
    
  } catch (error) {
    console.error('❌ Permission service authentication failed:', error);
    return {
      success: false,
      method: 'Permission Service Auth',
      error: `Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Get session cookie using permission service authentication
 */
export async function getPermissionServiceSessionCookie(): Promise<string | null> {
  const result = await authenticateWithPermissionService();
  
  if (result.success && result.cookieValue) {
    return `test_orders-portal=${result.cookieValue}`;
  }
  
  return null;
}
