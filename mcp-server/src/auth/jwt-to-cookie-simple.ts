/**
 * 🎯 Simple JWT to <PERSON>ie Converter
 * 
 * This module provides a simple way to convert existing JWT tokens
 * to pm_user_token cookies using the permission service flow.
 * 
 * Uses the existing mcp-mpc-odi.token.json file to simulate the
 * browser authentication flow and obtain a valid cookie.
 */

import axios from 'axios';
import { getEnvironmentConfig } from '../utils/env.js';
import { decodeJWTUnsafe } from '../utils/jwt-utils.js';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

export interface JWTToCookieConfig {
  permissionServiceBase: string;
  ordersApiBase: string;
  keycloakBaseUrl: string;
  keycloakRealm: string;
  clientId: string;
  redirectUrl: string;
}

export interface JWTToCookieResult {
  success: boolean;
  pmUserToken?: string;
  cookieValue?: string;
  expiresAt?: Date;
  method: string;
  error?: string;
}

/**
 * Load JWT token from file
 */
export function loadJWTToken(): { success: boolean; token?: string; userInfo?: any } {
  try {
    const tokenFile = path.join(process.cwd(), 'mcp-mpc-odi.token.json');
    const tokenContent = fs.readFileSync(tokenFile, 'utf8');
    const tokenData = JSON.parse(tokenContent);
    
    if (tokenData.access_token) {
      const payload = decodeJWTUnsafe(tokenData.access_token);
      return {
        success: true,
        token: tokenData.access_token,
        userInfo: {
          userId: payload.sub,
          email: payload.email,
          name: payload.name,
          sessionId: payload.sid,
          expiresAt: new Date(payload.exp * 1000)
        }
      };
    }
    
    return { success: false };
    
  } catch (error) {
    console.error('Failed to load JWT token:', error);
    return { success: false };
  }
}

/**
 * Load configuration for JWT to cookie conversion
 */
export function loadJWTToCookieConfig(): JWTToCookieConfig {
  const env = getEnvironmentConfig();
  
  return {
    permissionServiceBase: env.PERMISSION_SERVICE_BASE || 'https://api-dev-mpp-fe.ingka-dt.cn',
    ordersApiBase: env.ORDERS_API_BASE || 'https://fe-dev-i.ingka-dt.cn/order-web',
    keycloakBaseUrl: env.KEYCLOAK_BASE_URL || 'https://keycloak.ingka-dt.cn/auth',
    keycloakRealm: env.KEYCLOAK_REALM || 'master',
    clientId: 'orders-portal',
    redirectUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/user/current'
  };
}

/**
 * Create pm_user_token from JWT user info
 */
export function createPmUserTokenFromJWT(userInfo: any): {
  success: boolean;
  pmUserToken?: string;
  expiresAt?: Date;
} {
  try {
    console.log('🔄 Creating pm_user_token from JWT user info...');
    
    const currentTime = Date.now();
    const sessionTimeout = 3600; // 1 hour in seconds
    
    // Use existing session ID or generate new one
    const ssoUUID = userInfo.sessionId || crypto.randomUUID();
    const userId = userInfo.userId;
    
    console.log('📊 PM Token creation:');
    console.log(`   SSO UUID: ${ssoUUID}`);
    console.log(`   User ID: ${userId}`);
    console.log(`   User Email: ${userInfo.email}`);
    console.log(`   User Name: ${userInfo.name}`);
    console.log(`   Login Time: ${new Date(currentTime).toISOString()}`);
    console.log(`   Session Timeout: ${sessionTimeout}s`);
    
    // Create token in the format: <EMAIL>
    const tokenContent = `${ssoUUID}@${currentTime}.${userId}.${sessionTimeout}`;
    const pmUserToken = Buffer.from(tokenContent).toString('base64');
    
    const expiresAt = new Date(currentTime + (sessionTimeout * 1000));
    
    console.log('✅ PM user token created from JWT');
    console.log(`   Token: ${pmUserToken.substring(0, 50)}...`);
    console.log(`   Expires: ${expiresAt.toISOString()}`);
    
    return {
      success: true,
      pmUserToken,
      expiresAt
    };
    
  } catch (error) {
    console.error('❌ PM user token creation failed:', error);
    return { success: false };
  }
}

/**
 * Test the pm_user_token with orders API
 */
export async function testJWTBasedPmToken(
  pmUserToken: string,
  config: JWTToCookieConfig
): Promise<boolean> {
  try {
    console.log('🧪 Testing JWT-based pm_user_token with orders API...');
    
    const testUrl = `${config.ordersApiBase}/orders/search`;
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };
    
    const response = await axios.post(testUrl, requestData, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'content-type': 'application/json',
        'Cookie': `test_orders-portal=${pmUserToken}`,
        'origin': 'https://admin.ingka-dt.cn',
        'referer': 'https://admin.ingka-dt.cn/',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    
    const isWorking = response.status === 200 && 
                     response.data && 
                     response.data.code !== 401 && 
                     response.data.message !== 'to login';
    
    if (isWorking) {
      console.log('✅ JWT-based pm_user_token test successful');
      if (response.data.data) {
        console.log(`   Data preview: ${JSON.stringify(response.data.data, null, 2).substring(0, 200)}...`);
      }
    } else {
      console.log('❌ JWT-based pm_user_token test failed');
      console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
    }
    
    return isWorking;
    
  } catch (error) {
    console.error('❌ JWT-based pm_user_token test failed:', error);
    return false;
  }
}

/**
 * Try to use JWT token directly with permission service
 */
export async function tryJWTWithPermissionService(
  jwtToken: string,
  config: JWTToCookieConfig
): Promise<{
  success: boolean;
  pmUserToken?: string;
}> {
  try {
    console.log('🔄 Trying JWT token directly with permission service...');
    
    // Try to authenticate using the JWT token
    const authUrl = `${config.permissionServiceBase}/permission-service/user/auth/jwt-authenticate`;
    
    const response = await axios.post(authUrl, {
      token: jwtToken,
      clientId: config.clientId
    }, {
      headers: {
        'Authorization': `Bearer ${jwtToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200 && response.data && response.data.success) {
      console.log('✅ JWT authentication with permission service successful');
      
      // Extract pm_user_token from response
      const pmUserToken = response.data.data?.pmUserToken || response.data.pmUserToken;
      
      if (pmUserToken) {
        return {
          success: true,
          pmUserToken
        };
      }
    }
    
    return { success: false };
    
  } catch (error) {
    console.error('❌ JWT authentication with permission service failed:', error);
    return { success: false };
  }
}

/**
 * Main function: Convert JWT to cookie
 */
export async function convertJWTToCookie(): Promise<JWTToCookieResult> {
  console.log('🎯 ==================== JWT TO COOKIE CONVERSION ====================');
  console.log('🎯 Purpose: Convert existing JWT token to pm_user_token cookie\n');
  
  const config = loadJWTToCookieConfig();
  
  console.log('📋 Configuration:');
  console.log(`   Permission Service: ${config.permissionServiceBase}`);
  console.log(`   Orders API: ${config.ordersApiBase}`);
  console.log(`   Client ID: ${config.clientId}`);
  console.log(`   Redirect URL: ${config.redirectUrl}`);
  console.log('');
  
  // Step 1: Load JWT token
  const jwtResult = loadJWTToken();
  if (!jwtResult.success || !jwtResult.token) {
    return {
      success: false,
      method: 'JWT to Cookie',
      error: 'Failed to load JWT token from mcp-mpc-odi.token.json file'
    };
  }
  
  console.log('✅ JWT token loaded successfully');
  console.log(`   User: ${jwtResult.userInfo.name} (${jwtResult.userInfo.email})`);
  console.log(`   Expires: ${jwtResult.userInfo.expiresAt.toISOString()}`);
  console.log('');
  
  try {
    // Step 2: Try direct JWT authentication with permission service
    const directResult = await tryJWTWithPermissionService(jwtResult.token, config);
    
    if (directResult.success && directResult.pmUserToken) {
      console.log('✅ Direct JWT authentication successful');
      
      // Test the token
      const apiTest = await testJWTBasedPmToken(directResult.pmUserToken, config);
      
      if (apiTest) {
        return {
          success: true,
          pmUserToken: directResult.pmUserToken,
          cookieValue: directResult.pmUserToken,
          method: 'JWT Direct Auth',
          expiresAt: jwtResult.userInfo.expiresAt
        };
      }
    }
    
    // Step 3: Fallback - create pm_user_token from JWT user info
    console.log('🔄 Falling back to JWT-based token creation...');
    
    const pmTokenResult = createPmUserTokenFromJWT(jwtResult.userInfo);
    
    if (!pmTokenResult.success || !pmTokenResult.pmUserToken) {
      return {
        success: false,
        method: 'JWT to Cookie',
        error: 'Failed to create pm_user_token from JWT'
      };
    }
    
    // Step 4: Test the created token
    const apiTest = await testJWTBasedPmToken(pmTokenResult.pmUserToken, config);
    
    if (apiTest) {
      console.log('🎉 SUCCESS! JWT to cookie conversion completed successfully!');
      return {
        success: true,
        pmUserToken: pmTokenResult.pmUserToken,
        cookieValue: pmTokenResult.pmUserToken,
        method: 'JWT to Cookie',
        expiresAt: pmTokenResult.expiresAt
      };
    } else {
      return {
        success: false,
        method: 'JWT to Cookie',
        error: 'PM user token created but not accepted by orders API'
      };
    }
    
  } catch (error) {
    console.error('❌ JWT to cookie conversion failed:', error);
    return {
      success: false,
      method: 'JWT to Cookie',
      error: `Conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Get session cookie using JWT conversion
 */
export async function getJWTBasedSessionCookie(): Promise<string | null> {
  const result = await convertJWTToCookie();
  
  if (result.success && result.cookieValue) {
    return `test_orders-portal=${result.cookieValue}`;
  }
  
  return null;
}
