#!/usr/bin/env node

/**
 * 🔄 Simplified JWT to <PERSON><PERSON> Conversion
 * 
 * This approach tries to use the JWT token more directly
 * to get the test_orders-portal cookie
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const CONFIG = {
  jwtTokenFile: path.join(__dirname, '../jwt.token.json'),
  cookieOutputFile: path.join(__dirname, '../test_orders-portal.cookie'),
  timeout: 10000
};

/**
 * Load JWT token from file
 */
function loadJWTToken() {
  try {
    if (!fs.existsSync(CONFIG.jwtTokenFile)) {
      throw new Error(`JWT token file not found: ${CONFIG.jwtTokenFile}`);
    }
    
    const tokenData = JSON.parse(fs.readFileSync(CONFIG.jwtTokenFile, 'utf8'));
    
    if (!tokenData.access_token) {
      throw new Error('access_token not found in JWT token file');
    }
    
    console.log('✅ JWT token loaded successfully');
    return tokenData.access_token;
    
  } catch (error) {
    console.error('❌ Failed to load JWT token:', error.message);
    process.exit(1);
  }
}

/**
 * Try to get cookie by calling the order service directly with JWT
 */
async function tryDirectOrderServiceCall(jwtToken) {
  console.log('\n🔄 Trying direct Order Service call with JWT...');
  
  const testUrl = 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search';
  const testData = {
    storeIds: [],
    baseSize: 1,
    page: 1,
    size: 1,
    timestamp: Date.now()
  };
  
  try {
    const response = await fetch(testUrl, {
      method: 'POST',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`,
        'Origin': 'https://admin.ingka-dt.cn',
        'Referer': 'https://admin.ingka-dt.cn/',
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'X-Custom-Referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      body: JSON.stringify(testData)
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 200) {
      console.log('✅ Direct JWT call worked! No cookie conversion needed.');
      return null; // No cookie needed
    }
    
    // Check if response contains redirect or cookie info
    const setCookieHeaders = response.headers.get('set-cookie');
    if (setCookieHeaders) {
      console.log(`   Set-Cookie headers: ${setCookieHeaders}`);
      const cookieMatch = setCookieHeaders.match(/test_orders-portal=([^;]+)/);
      if (cookieMatch) {
        return cookieMatch[1];
      }
    }
    
    const responseText = await response.text();
    console.log(`   Response body (first 200 chars): ${responseText.substring(0, 200)}`);
    
    return null;
    
  } catch (error) {
    console.log(`   Direct call failed: ${error.message}`);
    return null;
  }
}

/**
 * Try to extract pm_user_token from JWT claims
 */
function tryExtractPmUserTokenFromJWT(jwtToken) {
  console.log('\n🔄 Trying to extract pm_user_token from JWT...');
  
  try {
    // JWT tokens have 3 parts: header.payload.signature
    const parts = jwtToken.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }
    
    // Decode the payload (second part)
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
    
    console.log('📋 JWT Claims:');
    console.log(`   Subject: ${payload.sub}`);
    console.log(`   Preferred Username: ${payload.preferred_username}`);
    console.log(`   Network ID: ${payload.networkId}`);
    console.log(`   Email: ${payload.email}`);
    console.log(`   Groups: ${payload.groups?.length || 0} groups`);
    
    // Try to construct pm_user_token from JWT claims
    // Format appears to be: <EMAIL> (Base64 encoded)
    const ssoUUID = payload.sub; // Use subject as SSO UUID
    const loginTime = payload.iat * 1000; // Convert to milliseconds
    const userId = payload.networkId || payload.preferred_username || payload.email;
    const sessionTimeout = 3600; // 1 hour default
    
    const tokenString = `${ssoUUID}@${loginTime}.${userId}.${sessionTimeout}`;
    const pmUserToken = Buffer.from(tokenString).toString('base64');
    
    console.log('🔧 Constructed pm_user_token:');
    console.log(`   Token string: ${tokenString}`);
    console.log(`   Base64 encoded: ${pmUserToken.substring(0, 50)}...`);
    
    return pmUserToken;
    
  } catch (error) {
    console.log(`   Failed to extract from JWT: ${error.message}`);
    return null;
  }
}

/**
 * Test a pm_user_token by calling the order service
 */
async function testPmUserToken(pmUserToken) {
  console.log('\n🧪 Testing pm_user_token with Order Service...');
  
  const testUrl = 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search';
  const testData = {
    storeIds: [],
    baseSize: 1,
    page: 1,
    size: 1,
    timestamp: Date.now()
  };
  
  try {
    const response = await fetch(testUrl, {
      method: 'POST',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Cookie': `test_orders-portal=${pmUserToken}`,
        'Origin': 'https://admin.ingka-dt.cn',
        'Referer': 'https://admin.ingka-dt.cn/',
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'X-Custom-Referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      body: JSON.stringify(testData)
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 200) {
      const responseData = await response.json();
      console.log('✅ pm_user_token works!');
      console.log(`   Response: ${JSON.stringify(responseData).substring(0, 100)}...`);
      return true;
    } else {
      const responseText = await response.text();
      console.log(`   Response: ${responseText.substring(0, 200)}...`);
      return false;
    }
    
  } catch (error) {
    console.log(`   Test failed: ${error.message}`);
    return false;
  }
}

/**
 * Save the cookie to file
 */
function saveCookie(cookie) {
  console.log('\n💾 Saving cookie to file...');
  
  const cookieData = {
    cookie: cookie,
    timestamp: new Date().toISOString(),
    expires: new Date(Date.now() + 3600 * 1000).toISOString(),
    domain: 'ingka-dt.cn',
    path: '/order-web',
    httpOnly: true
  };
  
  fs.writeFileSync(CONFIG.cookieOutputFile, JSON.stringify(cookieData, null, 2));
  
  console.log(`✅ Cookie saved to: ${CONFIG.cookieOutputFile}`);
  console.log(`   Cookie value: ${cookie.substring(0, 50)}...`);
}

/**
 * Main conversion function
 */
async function convertJWTToCookieSimple() {
  console.log('🔄 ==================== SIMPLIFIED JWT TO COOKIE CONVERSION ====================');
  console.log('🎯 Trying multiple approaches to get test_orders-portal cookie\n');
  
  try {
    // Load JWT token
    const jwtToken = loadJWTToken();
    
    // Approach 1: Try direct Order Service call with JWT
    const directCookie = await tryDirectOrderServiceCall(jwtToken);
    if (directCookie) {
      saveCookie(directCookie);
      console.log('\n🎉 Success with direct JWT approach!');
      return directCookie;
    }
    
    // Approach 2: Try to construct pm_user_token from JWT claims
    const pmUserToken = tryExtractPmUserTokenFromJWT(jwtToken);
    if (pmUserToken) {
      const works = await testPmUserToken(pmUserToken);
      if (works) {
        saveCookie(pmUserToken);
        console.log('\n🎉 Success with constructed pm_user_token!');
        return pmUserToken;
      }
    }
    
    // If we get here, both approaches failed
    console.log('\n💡 ==================== MANUAL APPROACH NEEDED ====================');
    console.log('❌ Automated conversion failed. Try manual approach:');
    console.log('');
    console.log('1. **Open browser and login to admin portal**');
    console.log('   https://admin.ingka-dt.cn/');
    console.log('');
    console.log('2. **Open Developer Tools → Network tab**');
    console.log('');
    console.log('3. **Navigate to Orders Portal and make a search**');
    console.log('');
    console.log('4. **Find a request to fe-dev-i.ingka-dt.cn/order-web/orders/search**');
    console.log('');
    console.log('5. **Copy the test_orders-portal cookie value**');
    console.log('');
    console.log('6. **Create test_orders-portal.cookie file manually:**');
    console.log('   {');
    console.log('     "cookie": "YOUR_COOKIE_VALUE_HERE",');
    console.log('     "timestamp": "' + new Date().toISOString() + '",');
    console.log('     "expires": "' + new Date(Date.now() + 3600 * 1000).toISOString() + '"');
    console.log('   }');
    
    throw new Error('Automated conversion failed - manual approach needed');
    
  } catch (error) {
    console.error('\n💥 ==================== CONVERSION FAILED ====================');
    console.error('❌ JWT to cookie conversion failed:', error.message);
    process.exit(1);
  }
}

// Run the conversion if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  convertJWTToCookieSimple();
}

export { convertJWTToCookieSimple };
