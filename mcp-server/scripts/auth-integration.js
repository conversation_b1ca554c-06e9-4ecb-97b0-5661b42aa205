#!/usr/bin/env node

/**
 * 🔗 Authentication Integration Helper
 * 
 * This script demonstrates how to integrate JWT-to-cookie conversion
 * into your existing auth module when you're ready.
 */

import { convertJWTToCookie } from './jwt-to-cookie.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Function that can be integrated into src/auth/auth.ts
 * Converts JWT token to test_orders-portal cookie
 */
export async function getOrdersPortalCookie(jwtToken) {
  try {
    // Save JWT token to temporary file
    const tempTokenFile = path.join(__dirname, '../jwt.token.temp.json');
    const tokenData = {
      access_token: jwtToken,
      token_type: 'Bearer',
      expires_in: 3600,
      scope: 'profile email'
    };
    
    fs.writeFileSync(tempTokenFile, JSON.stringify(tokenData, null, 2));
    
    // Convert JWT to cookie
    const cookie = await convertJWTToCookie();
    
    // Clean up temp file
    fs.unlinkSync(tempTokenFile);
    
    return cookie;
    
  } catch (error) {
    console.error('Failed to convert JWT to cookie:', error.message);
    throw error;
  }
}

/**
 * Enhanced authenticate function that includes cookie acquisition
 * This shows how you could modify your existing auth.ts
 */
export async function authenticateWithCookieAcquisition(req, oauth2Provider) {
  // First, do the existing OAuth2 validation
  const authResult = await validateToken(req, oauth2Provider);
  
  if (!authResult.success) {
    return authResult;
  }
  
  // If OAuth2 validation succeeded, get the orders portal cookie
  try {
    const jwtToken = req.headers.authorization?.substring(7); // Remove 'Bearer '
    const ordersPortalCookie = await getOrdersPortalCookie(jwtToken);
    
    // Add the cookie to the auth result
    authResult.ordersPortalCookie = ordersPortalCookie;
    
    console.log('✅ [Auth] JWT to cookie conversion successful');
    
    return authResult;
    
  } catch (error) {
    console.log('⚠️ [Auth] JWT to cookie conversion failed:', error.message);
    
    // Return the original auth result even if cookie conversion fails
    // This allows OAuth2 auth to work without breaking existing functionality
    return authResult;
  }
}

/**
 * Example of how to use the cookie in API calls
 */
export function createOrdersAPIClient(ordersPortalCookie) {
  return {
    async searchOrders(searchParams) {
      const response = await fetch('https://fe-dev-i.ingka-dt.cn/order-web/orders/search', {
        method: 'POST',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Content-Type': 'application/json',
          'Cookie': `test_orders-portal=${ordersPortalCookie}`,
          'Origin': 'https://admin.ingka-dt.cn',
          'Referer': 'https://admin.ingka-dt.cn/',
          'X-Custom-Referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
        },
        body: JSON.stringify(searchParams)
      });
      
      if (!response.ok) {
        throw new Error(`Orders API error: ${response.status} ${response.statusText}`);
      }
      
      return await response.json();
    }
  };
}

/**
 * Test the integration
 */
async function testIntegration() {
  console.log('🧪 Testing JWT to Cookie Integration...');
  
  try {
    // Check if JWT token file exists
    const jwtTokenFile = path.join(__dirname, '../jwt.token.json');
    if (!fs.existsSync(jwtTokenFile)) {
      console.log('⚠️ JWT token file not found. Please create jwt.token.json first.');
      console.log('   Run: npm run jwt-to-cookie');
      return;
    }
    
    // Load and test the conversion
    const tokenData = JSON.parse(fs.readFileSync(jwtTokenFile, 'utf8'));
    const cookie = await getOrdersPortalCookie(tokenData.access_token);
    
    console.log('✅ Integration test successful');
    console.log(`   Cookie: ${cookie.substring(0, 50)}...`);
    
    // Test API client
    const apiClient = createOrdersAPIClient(cookie);
    const searchResult = await apiClient.searchOrders({
      storeIds: [],
      baseSize: 1,
      page: 1,
      size: 1,
      timestamp: Date.now()
    });
    
    console.log('✅ API client test successful');
    console.log(`   Search result: ${JSON.stringify(searchResult).substring(0, 100)}...`);
    
  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
  }
}

// Show usage instructions
function showUsageInstructions() {
  console.log('🔗 ==================== AUTH INTEGRATION HELPER ====================');
  console.log('');
  console.log('📋 **How to integrate JWT-to-cookie conversion:**');
  console.log('');
  console.log('1. **First, test the standalone script:**');
  console.log('   npm run jwt-to-cookie');
  console.log('');
  console.log('2. **Then integrate into your auth module:**');
  console.log('   - Import getOrdersPortalCookie() function');
  console.log('   - Call it after successful OAuth2 validation');
  console.log('   - Use the returned cookie for Order Service API calls');
  console.log('');
  console.log('3. **Example integration in src/auth/auth.ts:**');
  console.log('   ```typescript');
  console.log('   import { getOrdersPortalCookie } from "../scripts/auth-integration.js";');
  console.log('   ');
  console.log('   // In your authenticate function:');
  console.log('   if (authResult.success) {');
  console.log('     const jwtToken = req.headers.authorization?.substring(7);');
  console.log('     authResult.ordersPortalCookie = await getOrdersPortalCookie(jwtToken);');
  console.log('   }');
  console.log('   ```');
  console.log('');
  console.log('4. **Test the integration:**');
  console.log('   node scripts/auth-integration.js');
  console.log('');
  console.log('🎯 **This completes Step 3: B Cookie Acquisition in your auth architecture!**');
}

// Run based on command line arguments
if (import.meta.url === `file://${process.argv[1]}`) {
  const command = process.argv[2];
  
  if (command === 'test') {
    testIntegration();
  } else {
    showUsageInstructions();
  }
}
