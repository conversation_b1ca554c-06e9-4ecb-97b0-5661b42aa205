#!/usr/bin/env node

/**
 * 🔄 JWT to <PERSON><PERSON> Conversion Script
 * 
 * Implements the missing Step 3: B <PERSON>ie Acquisition
 * Converts JWT token from permission-service to test_orders-portal cookie
 * 
 * Flow: JWT Token → toLogin → Keycloak Auth → loginComplete → pm_user_token → test_orders-portal cookie
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Configuration for the JWT to cookie conversion
 */
const CONFIG = {
  // API endpoints
  toLoginUrl: 'https://api-dev-mpp-fe.ingka-dt.cn/prm-auth/auth/toLogin',
  keycloakBaseUrl: 'https://keycloak.ingka-dt.cn/auth/realms/master/protocol/openid-connect',
  orderWebBaseUrl: 'https://fe-dev-i.ingka-dt.cn/order-web',
  
  // Client configuration
  clientId: 'orders-portal',
  redirectUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/user/current',
  bizOriginUrl: 'https://admin.ingka-dt.cn/app/orders-portal/oms/index',
  
  // JWT token file path
  jwtTokenFile: path.join(__dirname, '../jwt.token.json'),
  
  // Output file for the cookie
  cookieOutputFile: path.join(__dirname, '../test_orders-portal.cookie'),
  
  // Request timeout
  timeout: 10000
};

/**
 * Load JWT token from file
 */
function loadJWTToken() {
  try {
    if (!fs.existsSync(CONFIG.jwtTokenFile)) {
      throw new Error(`JWT token file not found: ${CONFIG.jwtTokenFile}`);
    }
    
    const tokenData = JSON.parse(fs.readFileSync(CONFIG.jwtTokenFile, 'utf8'));
    
    if (!tokenData.access_token) {
      throw new Error('access_token not found in JWT token file');
    }
    
    console.log('✅ JWT token loaded successfully');
    console.log(`   Token type: ${tokenData.token_type || 'Bearer'}`);
    console.log(`   Expires in: ${tokenData.expires_in || 'unknown'} seconds`);
    console.log(`   Scope: ${tokenData.scope || 'unknown'}`);
    
    return tokenData.access_token;
    
  } catch (error) {
    console.error('❌ Failed to load JWT token:', error.message);
    console.error('   Expected format: {"access_token": "...", "token_type": "Bearer", ...}');
    process.exit(1);
  }
}

/**
 * Generate a random UUID for state parameter
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Make HTTP request with redirect following
 */
async function makeRequest(url, options = {}) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), CONFIG.timeout);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      redirect: 'manual' // Handle redirects manually to capture Location headers
    });
    
    clearTimeout(timeoutId);
    return response;
    
  } catch (error) {
    clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
      throw new Error(`Request timeout after ${CONFIG.timeout}ms`);
    }
    throw error;
  }
}

/**
 * Alternative approach: Use the JWT token directly with Keycloak
 * Skip toLogin and go directly to Keycloak with the JWT token
 */
async function createKeycloakAuthUrl(jwtToken) {
  console.log('\n🔄 Step 1: Creating Keycloak auth URL...');

  const state = generateUUID();

  // Build Keycloak auth URL directly (similar to your captured request)
  const authUrl = `${CONFIG.keycloakBaseUrl}/auth` +
    `?response_type=code` +
    `&client_id=permission-service` +
    `&redirect_uri=${encodeURIComponent('https://api-dev-mpp-fe.ingka-dt.cn/prm-auth/auth/loginComplete')}` +
    `&state=${encodeURIComponent(state)}` +
    `&kc_idp_hint=PermissionCenterServerIDP` +
    `&scope=openid` +
    `&login=true`;

  console.log(`   Auth URL: ${authUrl}`);
  console.log(`   State: ${state}`);

  return { authUrl, state };
}

/**
 * Step 1: Call toLogin to initiate the authentication flow
 * Updated to handle the authentication properly
 */
async function callToLogin(jwtToken) {
  console.log('\n🔄 Step 1: Calling toLogin...');

  const state = generateUUID();
  const url = `${CONFIG.toLoginUrl}?clientId=${encodeURIComponent(CONFIG.clientId)}&redirectUrl=${encodeURIComponent(CONFIG.redirectUrl)}&state=${encodeURIComponent(state)}`;

  console.log(`   URL: ${url}`);
  console.log(`   State: ${state}`);

  // Try without Authorization header first (as per browser behavior)
  const response = await makeRequest(url, {
    method: 'GET',
    headers: {
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
      'Referer': 'https://admin.ingka-dt.cn/'
    }
  });

  if (response.status !== 302) {
    console.log(`   toLogin returned ${response.status}, trying alternative approach...`);

    // If toLogin fails, try direct Keycloak approach
    const { authUrl } = await createKeycloakAuthUrl(jwtToken);
    return { location: authUrl, state };
  }

  const location = response.headers.get('location');
  if (!location) {
    throw new Error('toLogin did not return a redirect location');
  }

  console.log('✅ toLogin successful');
  console.log(`   Redirect to: ${location}`);

  return { location, state };
}

/**
 * Step 2: Follow Keycloak auth redirect to get authorization code
 */
async function followKeycloakAuth(authUrl, jwtToken) {
  console.log('\n🔄 Step 2: Following Keycloak auth...');

  console.log(`   Auth URL: ${authUrl}`);

  // Create session cookies from JWT token for Keycloak
  const sessionCookies = [
    'KEYCLOAK_LOCALE=en',
    `KEYCLOAK_IDENTITY=${jwtToken}`,
    `KEYCLOAK_IDENTITY_LEGACY=${jwtToken}`
  ].join('; ');

  const response = await makeRequest(authUrl, {
    method: 'GET',
    headers: {
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
      'Cookie': sessionCookies,
      'Referer': 'https://admin.ingka-dt.cn/'
    }
  });

  if (response.status !== 302 && response.status !== 303) {
    console.log(`   Keycloak auth returned ${response.status}, checking response...`);
    const responseText = await response.text();
    console.log(`   Response body (first 200 chars): ${responseText.substring(0, 200)}`);
    throw new Error(`Keycloak auth failed: ${response.status} ${response.statusText}`);
  }

  const location = response.headers.get('location');
  if (!location) {
    throw new Error('Keycloak auth did not return a redirect location');
  }

  console.log('✅ Keycloak auth successful');
  console.log(`   Redirect to: ${location}`);

  return location;
}

/**
 * Step 3: Call loginComplete to get pm_user_token
 */
async function callLoginComplete(loginCompleteUrl, jwtToken) {
  console.log('\n🔄 Step 3: Calling loginComplete...');
  
  console.log(`   URL: ${loginCompleteUrl}`);
  
  const response = await makeRequest(loginCompleteUrl, {
    method: 'GET',
    headers: {
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
      'Authorization': `Bearer ${jwtToken}`
    }
  });
  
  if (response.status !== 302 && response.status !== 303) {
    console.log(`   loginComplete returned ${response.status}, checking response...`);
    const responseText = await response.text();
    console.log(`   Response body (first 200 chars): ${responseText.substring(0, 200)}`);
    throw new Error(`loginComplete failed: ${response.status} ${response.statusText}`);
  }
  
  const location = response.headers.get('location');
  if (!location) {
    throw new Error('loginComplete did not return a redirect location');
  }
  
  // Extract pm_user_token from the location URL
  const url = new URL(location);
  const pmUserToken = url.searchParams.get('pm_user_token');
  
  if (!pmUserToken) {
    throw new Error('pm_user_token not found in loginComplete redirect');
  }
  
  console.log('✅ loginComplete successful');
  console.log(`   pm_user_token: ${pmUserToken.substring(0, 50)}...`);
  console.log(`   Redirect to: ${location}`);
  
  return { pmUserToken, currentUrl: location };
}

/**
 * Step 4: Call current endpoint to get test_orders-portal cookie
 */
async function callCurrentEndpoint(currentUrl, jwtToken) {
  console.log('\n🔄 Step 4: Calling current endpoint...');
  
  console.log(`   URL: ${currentUrl}`);
  
  const response = await makeRequest(currentUrl, {
    method: 'GET',
    headers: {
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
      'Authorization': `Bearer ${jwtToken}`
    }
  });
  
  // Look for Set-Cookie header with test_orders-portal
  const setCookieHeaders = response.headers.get('set-cookie') || '';
  const cookieMatch = setCookieHeaders.match(/test_orders-portal=([^;]+)/);
  
  if (!cookieMatch) {
    console.log('⚠️ test_orders-portal cookie not found in Set-Cookie headers');
    console.log(`   Response status: ${response.status}`);
    console.log(`   Set-Cookie headers: ${setCookieHeaders}`);
    
    // Try to extract from response body or other headers
    const responseText = await response.text();
    console.log(`   Response body (first 200 chars): ${responseText.substring(0, 200)}`);
    
    throw new Error('test_orders-portal cookie not found');
  }
  
  const testOrdersPortalCookie = cookieMatch[1];
  
  console.log('✅ Current endpoint successful');
  console.log(`   test_orders-portal cookie: ${testOrdersPortalCookie.substring(0, 50)}...`);
  
  return testOrdersPortalCookie;
}

/**
 * Save the cookie to file
 */
function saveCookie(cookie) {
  console.log('\n💾 Saving cookie to file...');
  
  const cookieData = {
    cookie: cookie,
    timestamp: new Date().toISOString(),
    expires: new Date(Date.now() + 3600 * 1000).toISOString(), // 1 hour from now
    domain: 'ingka-dt.cn',
    path: '/order-web',
    httpOnly: true
  };
  
  fs.writeFileSync(CONFIG.cookieOutputFile, JSON.stringify(cookieData, null, 2));
  
  console.log(`✅ Cookie saved to: ${CONFIG.cookieOutputFile}`);
  console.log(`   Cookie value: ${cookie.substring(0, 50)}...`);
  console.log(`   Expires: ${cookieData.expires}`);
}

/**
 * Test the cookie by making a request to orders API
 */
async function testCookie(cookie) {
  console.log('\n🧪 Testing cookie with orders API...');
  
  const testUrl = `${CONFIG.orderWebBaseUrl}/orders/search`;
  const testData = {
    storeIds: [],
    baseSize: 1,
    page: 1,
    size: 1,
    timestamp: Date.now()
  };
  
  try {
    const response = await makeRequest(testUrl, {
      method: 'POST',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Cookie': `test_orders-portal=${cookie}`,
        'Origin': 'https://admin.ingka-dt.cn',
        'Referer': 'https://admin.ingka-dt.cn/',
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'X-Custom-Referrer': CONFIG.bizOriginUrl
      },
      body: JSON.stringify(testData)
    });
    
    if (response.status === 200) {
      console.log('✅ Cookie test successful - API call returned 200');
      const responseData = await response.json();
      console.log(`   Response: ${JSON.stringify(responseData).substring(0, 100)}...`);
    } else {
      console.log(`⚠️ Cookie test returned status: ${response.status}`);
      const responseText = await response.text();
      console.log(`   Response: ${responseText.substring(0, 200)}...`);
    }
    
  } catch (error) {
    console.log(`⚠️ Cookie test failed: ${error.message}`);
  }
}

/**
 * Main function to convert JWT to cookie
 */
async function convertJWTToCookie() {
  console.log('🔄 ==================== JWT TO COOKIE CONVERSION ====================');
  console.log('🎯 Converting JWT token to test_orders-portal cookie\n');
  
  try {
    // Load JWT token
    const jwtToken = loadJWTToken();
    
    // Step 1: Call toLogin
    const { location: authUrl, state } = await callToLogin(jwtToken);
    
    // Step 2: Follow Keycloak auth
    const loginCompleteUrl = await followKeycloakAuth(authUrl, jwtToken);
    
    // Step 3: Call loginComplete
    const { pmUserToken, currentUrl } = await callLoginComplete(loginCompleteUrl, jwtToken);
    
    // Step 4: Call current endpoint
    const testOrdersPortalCookie = await callCurrentEndpoint(currentUrl, jwtToken);
    
    // Save cookie
    saveCookie(testOrdersPortalCookie);
    
    // Test cookie
    await testCookie(testOrdersPortalCookie);
    
    console.log('\n🎉 ==================== CONVERSION COMPLETE ====================');
    console.log('✅ JWT token successfully converted to test_orders-portal cookie');
    console.log(`📁 Cookie saved to: ${CONFIG.cookieOutputFile}`);
    console.log('🔧 You can now use this cookie for API calls to the order service');
    
    return testOrdersPortalCookie;
    
  } catch (error) {
    console.error('\n💥 ==================== CONVERSION FAILED ====================');
    console.error('❌ JWT to cookie conversion failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Check that jwt.token.json exists and contains valid access_token');
    console.error('   2. Verify that the JWT token is not expired');
    console.error('   3. Ensure network connectivity to the APIs');
    console.error('   4. Check that the permission-service client has proper permissions');
    process.exit(1);
  }
}

// Run the conversion if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  convertJWTToCookie();
}

export { convertJWTToCookie };
