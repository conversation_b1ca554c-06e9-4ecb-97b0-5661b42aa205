#!/bin/bash

# 🚀 Orders Portal MCP Server Deployment Script
# Supports multiple environments and deployment strategies

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${1:-"development"}
ACTION=${2:-"up"}
VERSION=${VERSION:-"latest"}
REGISTRY=${REGISTRY:-""}

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    cat << EOF
🚀 Orders Portal MCP Server Deployment Script

Usage: $0 [ENVIRONMENT] [ACTION]

ENVIRONMENTS:
  development  - Local development with hot reload
  production   - Production deployment with security hardening
  test         - Testing environment

ACTIONS:
  up           - Start services (default)
  down         - Stop services
  restart      - Restart services
  build        - Build images only
  logs         - Show logs
  status       - Show service status
  clean        - Clean up containers and images

EXAMPLES:
  $0 development up
  $0 production build
  $0 production logs
  VERSION=v1.2.3 $0 production up

ENVIRONMENT VARIABLES:
  VERSION      - Image version tag (default: latest)
  REGISTRY     - Docker registry URL
  MCP_SERVER_PORT - Server port (default: 3000)

EOF
}

check_requirements() {
    log_info "Checking requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check if .env file exists for production
    if [[ "$ENVIRONMENT" == "production" ]] && [[ ! -f "$PROJECT_DIR/.env" ]]; then
        log_warning ".env file not found for production deployment"
        log_info "Creating .env template..."
        create_env_template
    fi
    
    log_success "Requirements check passed"
}

create_env_template() {
    # Copy the comprehensive .env.example if it exists
    if [[ -f "$PROJECT_DIR/.env.example" ]]; then
        cp "$PROJECT_DIR/.env.example" "$PROJECT_DIR/.env"
        log_info "Copied .env.example to .env"
    else
        # Create basic template if .env.example doesn't exist
        cat > "$PROJECT_DIR/.env" << EOF
# 🔧 Orders Portal MCP Server Environment Configuration
# Copy this file and update with your actual values

# Application Configuration
NODE_ENV=production
TRANSPORT=http
MCP_SERVER_PORT=3000
MCP_SERVER_BASE_URL=https://your-domain.com

# API Configuration
VITE_API_HOST=https://fe-dev-i.ingka-dt.cn
VITE_API_HOST_KONG=https://api-dev-mpp-fe.ingka-dt.cn
VITE_MASTER_DATA_API_HOST=https://master-data-api-dev.ingka-dt.cn
VITE_MASTER_DATA_API_KEY=your-api-key

# Authentication Configuration
AUTH_COOKIES=your-auth-cookies
X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/orders-portal/oms/index

# OAuth2 Configuration
OAUTH2_ENABLED=true
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn
KEYCLOAK_REALM=master
OAUTH2_CLIENT_ID=mcp-server
OAUTH2_CLIENT_SECRET=your-client-secret
OAUTH2_REDIRECT_URI=https://your-domain.com/auth/callback
OAUTH2_SCOPES=openid,profile,email

# Connection Authentication
CONNECTION_AUTH_ENABLED=true
CONNECTION_AUTH_STRICT=true
CONNECTION_AUTH_TEST_API=false

# Debug Configuration
DEBUG_SERVICE_ADAPTER=false

# Per-Request Authentication (Optional)
PER_REQUEST_AUTH_ENABLED=false
PER_REQUEST_AUTH_CACHE_ENABLED=true
PER_REQUEST_AUTH_CACHE_MAX_AGE=300
PER_REQUEST_AUTH_LOG_VALIDATION=false
EOF
    fi
    
    log_info "Created .env template at $PROJECT_DIR/.env"
    log_warning "Please update the .env file with your actual configuration values"
}

get_compose_files() {
    local compose_files="-f docker-compose.yml"
    
    case "$ENVIRONMENT" in
        "development")
            compose_files="$compose_files -f docker-compose.dev.yml"
            ;;
        "production")
            compose_files="$compose_files -f docker-compose.prod.yml"
            ;;
        "test")
            # Use base configuration for test
            ;;
        *)
            log_error "Unknown environment: $ENVIRONMENT"
            show_usage
            exit 1
            ;;
    esac
    
    echo "$compose_files"
}

build_images() {
    log_info "Building Docker images for $ENVIRONMENT environment..."
    
    cd "$PROJECT_DIR"
    
    local compose_files
    compose_files=$(get_compose_files)
    
    # Build images
    docker-compose $compose_files build --no-cache
    
    # Tag with version if provided
    if [[ -n "$REGISTRY" ]] && [[ "$VERSION" != "latest" ]]; then
        log_info "Tagging image with version $VERSION..."
        docker tag orders-portal-mcp-server:latest "$REGISTRY/orders-portal-mcp-server:$VERSION"
        docker tag orders-portal-mcp-server:latest "$REGISTRY/orders-portal-mcp-server:latest"
    fi
    
    log_success "Images built successfully"
}

deploy_services() {
    log_info "Deploying services for $ENVIRONMENT environment..."
    
    cd "$PROJECT_DIR"
    
    local compose_files
    compose_files=$(get_compose_files)
    
    # Start services
    docker-compose $compose_files up -d
    
    # Wait for health check
    log_info "Waiting for services to be healthy..."
    sleep 10
    
    # Check service status
    docker-compose $compose_files ps
    
    log_success "Services deployed successfully"
}

stop_services() {
    log_info "Stopping services..."
    
    cd "$PROJECT_DIR"
    
    local compose_files
    compose_files=$(get_compose_files)
    
    docker-compose $compose_files down
    
    log_success "Services stopped"
}

restart_services() {
    log_info "Restarting services..."
    stop_services
    deploy_services
}

show_logs() {
    cd "$PROJECT_DIR"
    
    local compose_files
    compose_files=$(get_compose_files)
    
    docker-compose $compose_files logs -f
}

show_status() {
    cd "$PROJECT_DIR"
    
    local compose_files
    compose_files=$(get_compose_files)
    
    log_info "Service Status:"
    docker-compose $compose_files ps
    
    log_info "Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

clean_up() {
    log_warning "Cleaning up containers and images..."
    
    cd "$PROJECT_DIR"
    
    # Stop and remove containers
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml -f docker-compose.prod.yml down --remove-orphans
    
    # Remove images
    docker rmi orders-portal-mcp-server:latest 2>/dev/null || true
    
    # Clean up unused resources
    docker system prune -f
    
    log_success "Cleanup completed"
}

# Main execution
main() {
    log_info "🚀 Orders Portal MCP Server Deployment"
    log_info "Environment: $ENVIRONMENT"
    log_info "Action: $ACTION"
    log_info "Version: $VERSION"
    
    if [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]]; then
        show_usage
        exit 0
    fi
    
    check_requirements
    
    case "$ACTION" in
        "up")
            build_images
            deploy_services
            ;;
        "down")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "build")
            build_images
            ;;
        "logs")
            show_logs
            ;;
        "status")
            show_status
            ;;
        "clean")
            clean_up
            ;;
        *)
            log_error "Unknown action: $ACTION"
            show_usage
            exit 1
            ;;
    esac
    
    log_success "Deployment script completed successfully! 🎉"
}

# Run main function with all arguments
main "$@"
