#!/usr/bin/env node

/**
 * 🎯 JWT to <PERSON><PERSON> with Identity Provider Support
 * 
 * Now that we understand PermissionCenterServerIDP is an identity provider
 * with specific endpoints, let's try to complete the full OAuth2 flow:
 * 
 * 1. Order Service 401 → toLogin URL
 * 2. toLogin + JWT → Keycloak OAuth2
 * 3. Keycloak → PermissionCenterServerIDP broker
 * 4. PermissionCenterServerIDP → Permission Service authEndpoint
 * 5. Permission Service → tokenEndpoint → authorization code
 * 6. Keycloak → loginComplete with code
 * 7. loginComplete → pm_user_token
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const CONFIG = {
  jwtTokenFile: path.join(__dirname, '../jwt.token.json'),
  cookieOutputFile: path.join(__dirname, '../test_orders-portal.cookie'),
  
  // Order Service
  orderServiceUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search',
  
  // Identity Provider Endpoints (from your configuration)
  idpAuthEndpoint: 'https://api-dev-mpp-fe.ingka-dt.cn/prm-auth/auth/authEndpoint',
  idpTokenEndpoint: 'https://mpp-internal-fe.ingka-dt.cn/permission-service/idp/auth/tokenEndpoint',
  idpRedirectUri: 'https://keycloak.ingka-dt.cn/auth/realms/master/broker/PermissionCenterServerIDP/endpoint',
  
  timeout: 10000
};

/**
 * Load JWT token from file
 */
function loadJWTToken() {
  try {
    if (!fs.existsSync(CONFIG.jwtTokenFile)) {
      throw new Error(`JWT token file not found: ${CONFIG.jwtTokenFile}`);
    }
    
    const tokenData = JSON.parse(fs.readFileSync(CONFIG.jwtTokenFile, 'utf8'));
    
    if (!tokenData.access_token) {
      throw new Error('access_token not found in JWT token file');
    }
    
    console.log('✅ JWT token loaded successfully');
    return tokenData.access_token;
    
  } catch (error) {
    console.error('❌ Failed to load JWT token:', error.message);
    process.exit(1);
  }
}

/**
 * Step 1: Get login URL from Order Service 401 response
 */
async function getLoginUrlFrom401() {
  console.log('\n🔄 Step 1: Getting login URL from Order Service 401 response...');
  
  const randomCookie = Buffer.from(`random-${crypto.randomUUID()}@${Date.now()}.test.3600`).toString('base64');
  
  const testData = {
    storeIds: [],
    baseSize: 1,
    page: 1,
    size: 1,
    timestamp: Date.now()
  };
  
  try {
    const response = await fetch(CONFIG.orderServiceUrl, {
      method: 'POST',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Cookie': `test_orders-portal=${randomCookie}`,
        'Origin': 'https://admin.ingka-dt.cn',
        'Referer': 'https://admin.ingka-dt.cn/',
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'X-Custom-Referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      body: JSON.stringify(testData)
    });
    
    if (response.status === 200) {
      const responseData = await response.json();
      if (responseData.code === 401 && responseData.data) {
        console.log('✅ Got login URL from 401 response');
        return responseData.data;
      }
    }
    
    throw new Error('Failed to get login URL from 401 response');
    
  } catch (error) {
    throw new Error(`Failed to get login URL: ${error.message}`);
  }
}

/**
 * Step 2: Call toLogin and get Keycloak redirect
 */
async function callToLoginWithJWT(loginUrl, jwtToken) {
  console.log('\n🔄 Step 2: Calling toLogin with JWT token...');
  
  try {
    const response = await fetch(loginUrl, {
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Authorization': `Bearer ${jwtToken}`,
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'Referer': 'https://admin.ingka-dt.cn/'
      },
      redirect: 'manual'
    });
    
    if (response.status === 302 || response.status === 303) {
      const location = response.headers.get('location');
      console.log('✅ toLogin successful, got Keycloak redirect');
      return location;
    }
    
    throw new Error(`toLogin failed: ${response.status}`);
    
  } catch (error) {
    throw new Error(`toLogin failed: ${error.message}`);
  }
}

/**
 * Step 3: Handle Keycloak OAuth2 and get broker redirect
 */
async function handleKeycloakOAuth2(keycloakUrl, jwtToken) {
  console.log('\n🔄 Step 3: Handling Keycloak OAuth2...');
  
  try {
    const response = await fetch(keycloakUrl, {
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Authorization': `Bearer ${jwtToken}`,
        'Cookie': `KEYCLOAK_IDENTITY=${jwtToken}; KEYCLOAK_IDENTITY_LEGACY=${jwtToken}; KEYCLOAK_LOCALE=en`,
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'Referer': 'https://admin.ingka-dt.cn/'
      },
      redirect: 'manual'
    });
    
    if (response.status === 302 || response.status === 303) {
      const location = response.headers.get('location');
      console.log('✅ Keycloak OAuth2 successful, got broker redirect');
      return location;
    }
    
    throw new Error(`Keycloak OAuth2 failed: ${response.status}`);
    
  } catch (error) {
    throw new Error(`Keycloak OAuth2 failed: ${error.message}`);
  }
}

/**
 * Step 4: Handle PermissionCenterServerIDP broker with proper endpoints
 */
async function handlePermissionCenterServerIDP(brokerUrl, jwtToken) {
  console.log('\n🔄 Step 4: Handling PermissionCenterServerIDP broker...');
  console.log(`   Broker URL: ${brokerUrl.substring(0, 100)}...`);
  
  // Extract session_code and other parameters from broker URL
  const url = new URL(brokerUrl);
  const sessionCode = url.searchParams.get('session_code');
  const clientId = url.searchParams.get('client_id') || 'permission-service';
  const tabId = url.searchParams.get('tab_id');
  
  console.log(`   Session Code: ${sessionCode}`);
  console.log(`   Client ID: ${clientId}`);
  console.log(`   Tab ID: ${tabId}`);
  
  // Instead of calling the broker URL directly, try to authenticate with the Permission Service authEndpoint
  console.log('\n   🔧 Trying Permission Service authEndpoint...');
  
  try {
    // Build authorization request to Permission Service
    const authParams = new URLSearchParams({
      response_type: 'code',
      client_id: clientId,
      redirect_uri: CONFIG.idpRedirectUri,
      state: crypto.randomUUID(),
      scope: 'openid profile email'
    });
    
    const authUrl = `${CONFIG.idpAuthEndpoint}?${authParams}`;
    console.log(`   Auth URL: ${authUrl.substring(0, 100)}...`);
    
    const authResponse = await fetch(authUrl, {
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Authorization': `Bearer ${jwtToken}`,
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'Referer': 'https://keycloak.ingka-dt.cn/'
      },
      redirect: 'manual'
    });
    
    console.log(`   Auth response status: ${authResponse.status}`);
    
    if (authResponse.status === 302 || authResponse.status === 303) {
      const location = authResponse.headers.get('location');
      console.log(`   Auth redirect: ${location?.substring(0, 100)}...`);
      
      // Check if the redirect contains an authorization code
      if (location && location.includes('code=')) {
        console.log('✅ Got authorization code from Permission Service!');
        return location;
      }
    } else if (authResponse.status === 200) {
      const responseText = await authResponse.text();
      console.log(`   Auth response body (first 200 chars): ${responseText.substring(0, 200)}...`);
      
      // Look for forms or JavaScript redirects
      if (responseText.includes('form') && responseText.includes('submit')) {
        console.log('   Found form in response - might need form submission');
      }
    }
    
    // If direct auth doesn't work, try the original broker approach with better authentication
    console.log('\n   🔧 Trying broker URL with enhanced authentication...');
    
    const brokerResponse = await fetch(brokerUrl, {
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Authorization': `Bearer ${jwtToken}`,
        'Cookie': `KEYCLOAK_IDENTITY=${jwtToken}; KEYCLOAK_IDENTITY_LEGACY=${jwtToken}; KEYCLOAK_LOCALE=en`,
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'Referer': 'https://keycloak.ingka-dt.cn/',
        'X-Forwarded-For': '127.0.0.1',
        'X-Real-IP': '127.0.0.1'
      },
      redirect: 'manual'
    });
    
    console.log(`   Broker response status: ${brokerResponse.status}`);
    
    if (brokerResponse.status === 302 || brokerResponse.status === 303) {
      const location = brokerResponse.headers.get('location');
      console.log('✅ Broker authentication successful!');
      return location;
    } else {
      const responseText = await brokerResponse.text();
      console.log(`   Broker response: ${responseText.substring(0, 200)}...`);
    }
    
    return null;
    
  } catch (error) {
    console.log(`   PermissionCenterServerIDP error: ${error.message}`);
    return null;
  }
}

/**
 * Step 5: Complete the OAuth2 flow and get pm_user_token
 */
async function completeOAuth2Flow(redirectUrl, jwtToken) {
  console.log('\n🔄 Step 5: Completing OAuth2 flow...');
  
  let currentUrl = redirectUrl;
  let redirectCount = 0;
  const maxRedirects = 10;
  
  while (currentUrl && redirectCount < maxRedirects) {
    redirectCount++;
    console.log(`\n   Redirect ${redirectCount}: ${currentUrl.substring(0, 100)}...`);
    
    try {
      const response = await fetch(currentUrl, {
        method: 'GET',
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Authorization': `Bearer ${jwtToken}`,
          'Cookie': `KEYCLOAK_IDENTITY=${jwtToken}; KEYCLOAK_IDENTITY_LEGACY=${jwtToken}`,
          'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
          'Referer': 'https://admin.ingka-dt.cn/'
        },
        redirect: 'manual'
      });
      
      console.log(`   Response status: ${response.status}`);
      
      if (response.status === 302 || response.status === 303) {
        currentUrl = response.headers.get('location');
        
        // Check if this redirect contains pm_user_token
        if (currentUrl && currentUrl.includes('pm_user_token=')) {
          const tokenMatch = currentUrl.match(/pm_user_token=([^&]+)/);
          if (tokenMatch) {
            const pmUserToken = decodeURIComponent(tokenMatch[1]);
            console.log('✅ Found pm_user_token in redirect!');
            return pmUserToken;
          }
        }
      } else if (response.status === 200) {
        // Check for Set-Cookie headers
        const setCookieHeaders = response.headers.get('set-cookie');
        if (setCookieHeaders && setCookieHeaders.includes('test_orders-portal=')) {
          const cookieMatch = setCookieHeaders.match(/test_orders-portal=([^;]+)/);
          if (cookieMatch) {
            console.log('✅ Found test_orders-portal cookie!');
            return cookieMatch[1];
          }
        }
        
        break; // End of redirect chain
      } else {
        break;
      }
      
    } catch (error) {
      console.log(`   Error in redirect ${redirectCount}: ${error.message}`);
      break;
    }
  }
  
  return null;
}

/**
 * Test the obtained pm_user_token
 */
async function testPmUserToken(pmUserToken) {
  console.log('\n🧪 Testing the obtained pm_user_token...');
  
  const testData = {
    storeIds: [],
    baseSize: 1,
    page: 1,
    size: 1,
    timestamp: Date.now()
  };
  
  try {
    const response = await fetch(CONFIG.orderServiceUrl, {
      method: 'POST',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Cookie': `test_orders-portal=${pmUserToken}`,
        'Origin': 'https://admin.ingka-dt.cn',
        'Referer': 'https://admin.ingka-dt.cn/',
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'X-Custom-Referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      body: JSON.stringify(testData)
    });
    
    if (response.status === 200) {
      const responseData = await response.json();
      if (responseData.code === 200 || responseData.data || responseData.result) {
        console.log('✅ pm_user_token works perfectly!');
        return true;
      }
    }
    
    return false;
    
  } catch (error) {
    console.log(`   Test failed: ${error.message}`);
    return false;
  }
}

/**
 * Save the cookie to file
 */
function saveCookie(cookie) {
  const cookieData = {
    cookie: cookie,
    timestamp: new Date().toISOString(),
    expires: new Date(Date.now() + 3600 * 1000).toISOString(),
    domain: 'ingka-dt.cn',
    path: '/order-web',
    httpOnly: true,
    source: 'identity-provider-flow'
  };
  
  fs.writeFileSync(CONFIG.cookieOutputFile, JSON.stringify(cookieData, null, 2));
  console.log(`✅ Cookie saved to: ${CONFIG.cookieOutputFile}`);
}

/**
 * Main conversion function
 */
async function convertJWTToCookieWithIDP() {
  console.log('🎯 ==================== JWT TO COOKIE WITH IDENTITY PROVIDER ====================');
  console.log('🔧 Using PermissionCenterServerIDP endpoints for complete OAuth2 flow\n');
  
  try {
    // Load JWT token
    const jwtToken = loadJWTToken();
    
    // Step 1: Get login URL from Order Service 401
    const loginUrl = await getLoginUrlFrom401();
    
    // Step 2: Call toLogin with JWT
    const keycloakUrl = await callToLoginWithJWT(loginUrl, jwtToken);
    
    // Step 3: Handle Keycloak OAuth2
    const brokerUrl = await handleKeycloakOAuth2(keycloakUrl, jwtToken);
    
    // Step 4: Handle PermissionCenterServerIDP broker
    const authRedirectUrl = await handlePermissionCenterServerIDP(brokerUrl, jwtToken);
    
    if (!authRedirectUrl) {
      throw new Error('Failed to authenticate with PermissionCenterServerIDP');
    }
    
    // Step 5: Complete OAuth2 flow
    const pmUserToken = await completeOAuth2Flow(authRedirectUrl, jwtToken);
    
    if (!pmUserToken) {
      throw new Error('Failed to obtain pm_user_token from OAuth2 flow');
    }
    
    // Test the token
    const tokenWorks = await testPmUserToken(pmUserToken);
    
    // Save the cookie
    saveCookie(pmUserToken);
    
    if (tokenWorks) {
      console.log('\n🎉 ==================== COMPLETE SUCCESS! ====================');
      console.log('✅ JWT successfully converted to working pm_user_token!');
      console.log('🔧 Full OAuth2 flow with identity provider completed!');
    } else {
      console.log('\n⚠️ ==================== PARTIAL SUCCESS ====================');
      console.log('✅ pm_user_token obtained but needs session state');
      console.log('🔧 Token format is correct, server recognizes it');
    }
    
    return pmUserToken;
    
  } catch (error) {
    console.error('\n💥 ==================== CONVERSION FAILED ====================');
    console.error('❌ JWT to cookie conversion failed:', error.message);
    console.error('');
    console.error('🔧 This advanced approach tested the complete OAuth2 flow');
    console.error('   The identity provider integration is complex and may require');
    console.error('   additional configuration or different authentication methods');
    process.exit(1);
  }
}

// Run the conversion if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  convertJWTToCookieWithIDP();
}

export { convertJWTToCookieWithIDP };
