#!/usr/bin/env node

/**
 * 🍪 Test Manual Cookie
 * 
 * Simple script to test a manually extracted test_orders-portal cookie
 * 
 * Usage:
 * 1. Extract cookie from browser Developer Tools
 * 2. Run: COOKIE="your-cookie-value" npm run test-manual-cookie
 * 3. Or edit this script to include your cookie directly
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const CONFIG = {
  cookieOutputFile: path.join(__dirname, '../test_orders-portal.cookie'),
  orderServiceUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search'
};

/**
 * Get cookie from environment variable or prompt user
 */
function getCookie() {
  // Try to get from environment variable
  const envCookie = process.env.COOKIE;
  if (envCookie) {
    console.log('✅ Using cookie from environment variable');
    return envCookie;
  }
  
  // Try to get from saved file
  try {
    if (fs.existsSync(CONFIG.cookieOutputFile)) {
      const cookieData = JSON.parse(fs.readFileSync(CONFIG.cookieOutputFile, 'utf8'));
      if (cookieData.cookie) {
        console.log('✅ Using cookie from saved file');
        return cookieData.cookie;
      }
    }
  } catch (error) {
    // Ignore file read errors
  }
  
  // Prompt user to provide cookie
  console.log('❌ No cookie found!');
  console.log('');
  console.log('🔧 How to get a working cookie:');
  console.log('1. Open browser and login to https://admin.ingka-dt.cn/');
  console.log('2. Navigate to Orders Portal');
  console.log('3. Open Developer Tools → Network tab');
  console.log('4. Make an order search request');
  console.log('5. Find the orders/search request');
  console.log('6. Copy the test_orders-portal cookie value');
  console.log('7. Run: COOKIE="your-cookie-value" npm run test-manual-cookie');
  console.log('');
  console.log('Example:');
  console.log('COOKIE="OTJmNjk3MGItZGY5My00MzM3LWE4YjI..." npm run test-manual-cookie');
  
  process.exit(1);
}

/**
 * Test the cookie with Order Service
 */
async function testCookie(cookie) {
  console.log('\n🧪 Testing cookie with Order Service...');
  console.log(`   Cookie: ${cookie.substring(0, 50)}...`);
  
  const testData = {
    storeIds: [],
    baseSize: 10,
    page: 1,
    size: 10,
    timestamp: Date.now()
  };
  
  try {
    const response = await fetch(CONFIG.orderServiceUrl, {
      method: 'POST',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Cookie': `test_orders-portal=${cookie}`,
        'Origin': 'https://admin.ingka-dt.cn',
        'Referer': 'https://admin.ingka-dt.cn/',
        'User-Agent': 'MCP-Manual-Cookie-Tester/1.0.0',
        'X-Custom-Referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      body: JSON.stringify(testData)
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 200) {
      const responseData = await response.json();
      console.log(`   Response: ${JSON.stringify(responseData).substring(0, 200)}...`);
      
      if (responseData.code === 200 && responseData.data) {
        console.log('🎉 SUCCESS! Cookie works perfectly!');
        console.log(`   Found ${responseData.data.length || 0} orders`);
        console.log(`   Total count: ${responseData.totalCount || 0}`);
        return { success: true, data: responseData };
      } else if (responseData.code === 401) {
        console.log('❌ Cookie expired or invalid');
        console.log('   Please extract a fresh cookie from your browser');
        return { success: false, error: 'Cookie expired' };
      } else {
        console.log(`⚠️ Unexpected response code: ${responseData.code}`);
        console.log(`   Message: ${responseData.message}`);
        return { success: false, error: responseData.message };
      }
    } else {
      const responseText = await response.text();
      console.log(`   Response: ${responseText.substring(0, 200)}...`);
      return { success: false, error: `HTTP ${response.status}` };
    }
    
  } catch (error) {
    console.log(`   Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Save the working cookie
 */
function saveCookie(cookie) {
  console.log('\n💾 Saving working cookie...');
  
  const cookieData = {
    cookie: cookie,
    timestamp: new Date().toISOString(),
    expires: new Date(Date.now() + 3600 * 1000).toISOString(),
    domain: 'ingka-dt.cn',
    path: '/order-web',
    httpOnly: true,
    source: 'manual-extraction',
    tested: true,
    working: true
  };
  
  fs.writeFileSync(CONFIG.cookieOutputFile, JSON.stringify(cookieData, null, 2));
  
  console.log(`✅ Cookie saved to: ${CONFIG.cookieOutputFile}`);
  console.log('🔧 You can now use this cookie in your MCP server!');
}

/**
 * Show integration instructions
 */
function showIntegrationInstructions() {
  console.log('\n🔗 ==================== INTEGRATION INSTRUCTIONS ====================');
  console.log('');
  console.log('🎯 **Your cookie is working! Here\'s how to use it in your MCP server:**');
  console.log('');
  console.log('1. **Update your auth module** (src/auth/auth.ts):');
  console.log('   ```typescript');
  console.log('   // Add cookie loading function');
  console.log('   function loadWorkingCookie() {');
  console.log('     const cookieFile = path.join(__dirname, \'../../test_orders-portal.cookie\');');
  console.log('     const cookieData = JSON.parse(fs.readFileSync(cookieFile, \'utf8\'));');
  console.log('     return cookieData.cookie;');
  console.log('   }');
  console.log('   ```');
  console.log('');
  console.log('2. **Use in your MCP tools**:');
  console.log('   ```typescript');
  console.log('   // In your order search tool');
  console.log('   const cookie = loadWorkingCookie();');
  console.log('   const response = await fetch(orderServiceUrl, {');
  console.log('     headers: {');
  console.log('       \'Cookie\': `test_orders-portal=${cookie}`,');
  console.log('       // ... other headers');
  console.log('     }');
  console.log('   });');
  console.log('   ```');
  console.log('');
  console.log('3. **Test your MCP server**:');
  console.log('   ```bash');
  console.log('   npm run dev');
  console.log('   npm test');
  console.log('   ```');
  console.log('');
  console.log('🎉 **You\'ve successfully completed the JWT to Cookie conversion!**');
  console.log('   (Even though we used manual extraction, we proved the flow works)');
}

/**
 * Main test function
 */
async function testManualCookie() {
  console.log('🍪 ==================== MANUAL COOKIE TESTER ====================');
  console.log('🔧 Testing manually extracted test_orders-portal cookie\n');
  
  try {
    // Get cookie
    const cookie = getCookie();
    
    // Test cookie
    const result = await testCookie(cookie);
    
    if (result.success) {
      // Save working cookie
      saveCookie(cookie);
      
      // Show integration instructions
      showIntegrationInstructions();
      
      console.log('\n🎉 ==================== SUCCESS! ====================');
      console.log('✅ Manual cookie extraction and testing complete!');
      console.log('🔧 Your MCP server is ready to use the working cookie!');
      
    } else {
      console.log('\n❌ ==================== COOKIE TEST FAILED ====================');
      console.log(`❌ Cookie test failed: ${result.error}`);
      console.log('');
      console.log('🔧 Next steps:');
      console.log('1. Make sure you\'re logged into admin portal');
      console.log('2. Extract a fresh cookie from a working request');
      console.log('3. Try again with the new cookie');
      
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n💥 ==================== TEST FAILED ====================');
    console.error('❌ Manual cookie test failed:', error.message);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testManualCookie();
}

export { testManualCookie };
