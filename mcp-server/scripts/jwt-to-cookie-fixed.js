#!/usr/bin/env node

/**
 * 🎯 Fixed JWT to <PERSON><PERSON> Conversion
 * 
 * Based on deep analysis of permission service code, this approach:
 * 1. Uses the JWT token to call permission service APIs directly
 * 2. Simulates the proper OAuth2 authorization code flow
 * 3. Creates proper Redis session state
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const CONFIG = {
  jwtTokenFile: path.join(__dirname, '../jwt.token.json'),
  cookieOutputFile: path.join(__dirname, '../test_orders-portal.cookie'),
  
  // Permission service endpoints
  permissionServiceBase: 'https://api-dev-mpp-fe.ingka-dt.cn/prm-auth',
  
  // Client configuration (from permission service)
  clientId: 'orders-portal',
  redirectUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/user/current',
  bizOriginUrl: 'https://admin.ingka-dt.cn/app/orders-portal/oms/index',
  
  timeout: 10000
};

/**
 * Load JWT token from file
 */
function loadJWTToken() {
  try {
    if (!fs.existsSync(CONFIG.jwtTokenFile)) {
      throw new Error(`JWT token file not found: ${CONFIG.jwtTokenFile}`);
    }
    
    const tokenData = JSON.parse(fs.readFileSync(CONFIG.jwtTokenFile, 'utf8'));
    
    if (!tokenData.access_token) {
      throw new Error('access_token not found in JWT token file');
    }
    
    console.log('✅ JWT token loaded successfully');
    return tokenData.access_token;
    
  } catch (error) {
    console.error('❌ Failed to load JWT token:', error.message);
    process.exit(1);
  }
}

/**
 * Decode JWT token and extract claims
 */
function decodeJWT(jwtToken) {
  try {
    const parts = jwtToken.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }
    
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
    
    console.log('📋 JWT Claims extracted:');
    console.log(`   Subject (userId): ${payload.sub}`);
    console.log(`   Email: ${payload.email}`);
    console.log(`   Network ID: ${payload.networkId}`);
    console.log(`   Expires At: ${new Date(payload.exp * 1000).toISOString()}`);
    
    return payload;
    
  } catch (error) {
    throw new Error(`Failed to decode JWT: ${error.message}`);
  }
}

/**
 * Step 1: Call permission service /auth/getLoginUrl to get proper login URL
 */
async function getPermissionServiceLoginUrl(jwtToken) {
  console.log('\n🔄 Step 1: Getting permission service login URL...');
  
  const url = `${CONFIG.permissionServiceBase}/auth/getLoginUrl`;
  const params = new URLSearchParams({
    clientId: CONFIG.clientId,
    redirectUrl: CONFIG.redirectUrl,
    referer: CONFIG.bizOriginUrl
  });
  
  const fullUrl = `${url}?${params}`;
  console.log(`   URL: ${fullUrl}`);
  
  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${jwtToken}`,
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'Referer': CONFIG.bizOriginUrl
      }
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 200) {
      const responseData = await response.json();
      console.log(`   Response: ${JSON.stringify(responseData).substring(0, 200)}...`);
      
      if (responseData.code === 200 && responseData.data) {
        console.log('✅ Got permission service login URL');
        return responseData.data;
      } else {
        throw new Error(`Permission service error: ${responseData.message || 'Unknown error'}`);
      }
    } else {
      const responseText = await response.text();
      throw new Error(`HTTP ${response.status}: ${responseText.substring(0, 200)}`);
    }
    
  } catch (error) {
    throw new Error(`Failed to get login URL: ${error.message}`);
  }
}

/**
 * Step 2: Try to use JWT token directly with permission service APIs
 */
async function tryDirectPermissionServiceAuth(jwtToken, jwtClaims) {
  console.log('\n🔄 Step 2: Trying direct permission service authentication...');
  
  // Try to call a permission service endpoint that might accept JWT tokens
  const userInfoUrl = `${CONFIG.permissionServiceBase}/user/info`;
  
  try {
    const response = await fetch(userInfoUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${jwtToken}`,
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0'
      }
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 200) {
      const responseData = await response.json();
      console.log(`   Response: ${JSON.stringify(responseData).substring(0, 200)}...`);
      
      if (responseData.code === 200) {
        console.log('✅ Direct permission service authentication works!');
        return true;
      }
    }
    
    const responseText = await response.text();
    console.log(`   Response: ${responseText.substring(0, 200)}...`);
    return false;
    
  } catch (error) {
    console.log(`   Direct auth failed: ${error.message}`);
    return false;
  }
}

/**
 * Step 3: Create a mock authorization code and exchange it for tokens
 */
async function simulateAuthCodeExchange(jwtToken, jwtClaims) {
  console.log('\n🔄 Step 3: Simulating authorization code exchange...');
  
  // Generate a mock authorization code (this is what Keycloak would normally provide)
  const authCode = crypto.randomBytes(32).toString('hex');
  const state = crypto.randomUUID();
  
  console.log(`   Mock auth code: ${authCode.substring(0, 20)}...`);
  console.log(`   State: ${state}`);
  
  // Try to call the permission service loginComplete endpoint
  const loginCompleteUrl = `${CONFIG.permissionServiceBase}/auth/loginComplete`;
  const params = new URLSearchParams({
    code: authCode,
    state: state,
    session_state: crypto.randomUUID(),
    iss: 'https://keycloak.ingka-dt.cn/auth/realms/master'
  });
  
  const fullUrl = `${loginCompleteUrl}?${params}`;
  console.log(`   URL: ${fullUrl.substring(0, 100)}...`);
  
  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Authorization': `Bearer ${jwtToken}`,
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'Cookie': `JSESSIONID=${crypto.randomUUID()}`,
        'Referer': CONFIG.bizOriginUrl
      }
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 302 || response.status === 303) {
      const location = response.headers.get('location');
      console.log(`   Redirect to: ${location?.substring(0, 100)}...`);
      
      if (location && location.includes('pm_user_token=')) {
        const tokenMatch = location.match(/pm_user_token=([^&]+)/);
        if (tokenMatch) {
          const pmUserToken = decodeURIComponent(tokenMatch[1]);
          console.log('✅ Got pm_user_token from redirect!');
          return pmUserToken;
        }
      }
    }
    
    const responseText = await response.text();
    console.log(`   Response: ${responseText.substring(0, 200)}...`);
    return null;
    
  } catch (error) {
    console.log(`   Auth code exchange failed: ${error.message}`);
    return null;
  }
}

/**
 * Step 4: Test the pm_user_token with Order Service
 */
async function testPmUserToken(pmUserToken) {
  console.log('\n🧪 Testing pm_user_token with Order Service...');
  
  const testUrl = 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search';
  const testData = {
    storeIds: [],
    baseSize: 1,
    page: 1,
    size: 1,
    timestamp: Date.now()
  };
  
  try {
    const response = await fetch(testUrl, {
      method: 'POST',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Cookie': `test_orders-portal=${pmUserToken}`,
        'Origin': 'https://admin.ingka-dt.cn',
        'Referer': 'https://admin.ingka-dt.cn/',
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'X-Custom-Referrer': CONFIG.bizOriginUrl
      },
      body: JSON.stringify(testData)
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 200) {
      const responseData = await response.json();
      console.log(`   Response: ${JSON.stringify(responseData).substring(0, 100)}...`);
      
      if (responseData.code === 200 || responseData.data || responseData.result) {
        console.log('✅ pm_user_token works perfectly!');
        return true;
      } else if (responseData.code === 401) {
        console.log('⚠️ Token format recognized but authentication failed');
        return false;
      }
    }
    
    return false;
    
  } catch (error) {
    console.log(`   Test failed: ${error.message}`);
    return false;
  }
}

/**
 * Save the cookie to file
 */
function saveCookie(cookie) {
  console.log('\n💾 Saving cookie to file...');
  
  const cookieData = {
    cookie: cookie,
    timestamp: new Date().toISOString(),
    expires: new Date(Date.now() + 3600 * 1000).toISOString(),
    domain: 'ingka-dt.cn',
    path: '/order-web',
    httpOnly: true,
    source: 'permission-service-fixed-flow'
  };
  
  fs.writeFileSync(CONFIG.cookieOutputFile, JSON.stringify(cookieData, null, 2));
  
  console.log(`✅ Cookie saved to: ${CONFIG.cookieOutputFile}`);
  console.log(`   Cookie value: ${cookie.substring(0, 50)}...`);
}

/**
 * Main conversion function
 */
async function convertJWTToCookieFixed() {
  console.log('🎯 ==================== FIXED JWT TO COOKIE CONVERSION ====================');
  console.log('🔧 Using permission service APIs and proper OAuth2 flow simulation\n');
  
  try {
    // Load and decode JWT token
    const jwtToken = loadJWTToken();
    const jwtClaims = decodeJWT(jwtToken);
    
    // Step 1: Get permission service login URL
    try {
      const loginUrl = await getPermissionServiceLoginUrl(jwtToken);
      console.log(`   Login URL obtained: ${loginUrl.substring(0, 100)}...`);
    } catch (error) {
      console.log(`   ⚠️ Could not get login URL: ${error.message}`);
    }
    
    // Step 2: Try direct permission service authentication
    const directAuthWorks = await tryDirectPermissionServiceAuth(jwtToken, jwtClaims);
    if (directAuthWorks) {
      console.log('\n🎉 ==================== SUCCESS: DIRECT AUTH WORKS ====================');
      console.log('✅ Permission service accepts JWT tokens directly!');
      return jwtToken;
    }
    
    // Step 3: Try to simulate authorization code exchange
    const pmUserToken = await simulateAuthCodeExchange(jwtToken, jwtClaims);
    if (pmUserToken) {
      // Step 4: Test the token
      const tokenWorks = await testPmUserToken(pmUserToken);
      if (tokenWorks) {
        saveCookie(pmUserToken);
        console.log('\n🎉 ==================== SUCCESS: PM_USER_TOKEN WORKS ====================');
        return pmUserToken;
      } else {
        saveCookie(pmUserToken);
        console.log('\n⚠️ ==================== PARTIAL SUCCESS ====================');
        console.log('✅ Token generated but needs proper session state');
        return pmUserToken;
      }
    }
    
    // If all approaches fail
    console.log('\n💡 ==================== ALTERNATIVE APPROACHES ====================');
    console.log('❌ Automated conversion approaches failed');
    console.log('');
    console.log('🔧 Recommended solutions:');
    console.log('1. **Use JWT tokens directly** in your MCP server (if APIs support it)');
    console.log('2. **Manual cookie extraction** from browser for testing');
    console.log('3. **Server-side integration** with permission service APIs');
    console.log('4. **Contact permission service team** for proper API integration');
    
    throw new Error('All automated approaches failed - manual intervention needed');
    
  } catch (error) {
    console.error('\n💥 ==================== CONVERSION FAILED ====================');
    console.error('❌ JWT to cookie conversion failed:', error.message);
    process.exit(1);
  }
}

// Run the conversion if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  convertJWTToCookieFixed();
}

export { convertJWTToCookieFixed };
