#!/usr/bin/env node

/**
 * 🎯 Real Permission Service API JWT to <PERSON>ie Conversion
 * 
 * Uses the actual permission service backend APIs:
 * - /permission-service/user/auth/getLoginUrl (with clientId/clientSecret headers)
 * - /permission-service/idp/auth/userInfo (with Authorization header)
 * - /prm-auth/auth/toLogin and /prm-auth/auth/loginComplete (BFF endpoints)
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const CONFIG = {
  jwtTokenFile: path.join(__dirname, '../jwt.token.json'),
  cookieOutputFile: path.join(__dirname, '../test_orders-portal.cookie'),
  
  // Permission service endpoints (need to determine actual host/port)
  permissionServiceBackend: 'https://api-dev-mpp-fe.ingka-dt.cn/permission-service',
  permissionServiceBFF: 'https://api-dev-mpp-fe.ingka-dt.cn/prm-auth',
  
  // Client configuration for SDK authentication
  clientId: 'orders-portal',
  clientSecret: 'your-client-secret', // This needs to be provided
  
  // Target configuration
  redirectUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/user/current',
  referer: 'https://admin.ingka-dt.cn/app/orders-portal/oms/index',
  
  timeout: 10000
};

/**
 * Load JWT token from file
 */
function loadJWTToken() {
  try {
    if (!fs.existsSync(CONFIG.jwtTokenFile)) {
      throw new Error(`JWT token file not found: ${CONFIG.jwtTokenFile}`);
    }
    
    const tokenData = JSON.parse(fs.readFileSync(CONFIG.jwtTokenFile, 'utf8'));
    
    if (!tokenData.access_token) {
      throw new Error('access_token not found in JWT token file');
    }
    
    console.log('✅ JWT token loaded successfully');
    return tokenData.access_token;
    
  } catch (error) {
    console.error('❌ Failed to load JWT token:', error.message);
    process.exit(1);
  }
}

/**
 * Step 1: Call permission service backend /user/auth/getLoginUrl
 * This should return a URL to the BFF toLogin endpoint
 */
async function getPermissionServiceLoginUrl(jwtToken) {
  console.log('\n🔄 Step 1: Getting login URL from permission service backend...');
  
  const url = `${CONFIG.permissionServiceBackend}/user/auth/getLoginUrl`;
  const params = new URLSearchParams({
    redirectUrl: CONFIG.redirectUrl,
    referer: CONFIG.referer,
    internalNet: 'true'
  });
  
  const fullUrl = `${url}?${params}`;
  console.log(`   URL: ${fullUrl}`);
  
  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'clientId': CONFIG.clientId,
        'secret': CONFIG.clientSecret,
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0'
      }
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 200) {
      const responseData = await response.json();
      console.log(`   Response: ${JSON.stringify(responseData).substring(0, 200)}...`);
      
      if (responseData.code === 200 && responseData.data) {
        console.log('✅ Got permission service login URL');
        return responseData.data;
      } else {
        throw new Error(`Permission service error: ${responseData.message || 'Unknown error'}`);
      }
    } else {
      const responseText = await response.text();
      throw new Error(`HTTP ${response.status}: ${responseText.substring(0, 200)}`);
    }
    
  } catch (error) {
    throw new Error(`Failed to get login URL: ${error.message}`);
  }
}

/**
 * Step 2: Try to get user info using JWT token with permission service
 */
async function getUserInfoFromPermissionService(jwtToken) {
  console.log('\n🔄 Step 2: Getting user info from permission service...');
  
  const url = `${CONFIG.permissionServiceBackend}/idp/auth/userInfo`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${jwtToken}`,
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0'
      }
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 200) {
      const responseData = await response.json();
      console.log(`   Response: ${JSON.stringify(responseData).substring(0, 200)}...`);
      console.log('✅ Got user info from permission service');
      return responseData;
    } else {
      const responseText = await response.text();
      console.log(`   Response: ${responseText.substring(0, 200)}...`);
      return null;
    }
    
  } catch (error) {
    console.log(`   Failed to get user info: ${error.message}`);
    return null;
  }
}

/**
 * Step 3: Try to call BFF toLogin endpoint with proper state
 */
async function callBFFToLogin(loginUrl, jwtToken) {
  console.log('\n🔄 Step 3: Calling BFF toLogin endpoint...');
  
  // Extract parameters from the login URL
  const urlObj = new URL(loginUrl);
  const clientId = urlObj.searchParams.get('clientId');
  const redirectUrl = urlObj.searchParams.get('redirectUrl');
  const state = urlObj.searchParams.get('state');
  
  console.log(`   Client ID: ${clientId}`);
  console.log(`   Redirect URL: ${redirectUrl}`);
  console.log(`   State: ${state}`);
  
  try {
    const response = await fetch(loginUrl, {
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Authorization': `Bearer ${jwtToken}`,
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'Referer': CONFIG.referer
      },
      redirect: 'manual'
    });
    
    console.log(`   Response status: ${response.status}`);
    
    if (response.status === 302 || response.status === 303) {
      const location = response.headers.get('location');
      console.log(`   Redirect to: ${location?.substring(0, 100)}...`);
      
      if (location) {
        console.log('✅ BFF toLogin successful, got redirect');
        return location;
      }
    }
    
    const responseText = await response.text();
    console.log(`   Response: ${responseText.substring(0, 200)}...`);
    return null;
    
  } catch (error) {
    console.log(`   BFF toLogin failed: ${error.message}`);
    return null;
  }
}

/**
 * Step 4: Test different permission service endpoints
 */
async function testPermissionServiceEndpoints(jwtToken) {
  console.log('\n🔄 Step 4: Testing various permission service endpoints...');
  
  const endpoints = [
    `${CONFIG.permissionServiceBackend}/rs/user/info`,
    `${CONFIG.permissionServiceBackend}/third/api/org/microsoft/user/<EMAIL>`,
    `${CONFIG.permissionServiceBFF}/auth/check`
  ];
  
  for (const endpoint of endpoints) {
    console.log(`\n   Testing: ${endpoint}`);
    
    try {
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${jwtToken}`,
          'clientId': CONFIG.clientId,
          'secret': CONFIG.clientSecret,
          'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0'
        }
      });
      
      console.log(`   Status: ${response.status}`);
      
      if (response.status === 200) {
        const responseData = await response.json();
        console.log(`   Response: ${JSON.stringify(responseData).substring(0, 100)}...`);
      } else {
        const responseText = await response.text();
        console.log(`   Response: ${responseText.substring(0, 100)}...`);
      }
      
    } catch (error) {
      console.log(`   Error: ${error.message}`);
    }
  }
}

/**
 * Step 5: Try to create a pm_user_token using the JWT claims and test it
 */
async function createAndTestPmUserToken(jwtToken) {
  console.log('\n🔄 Step 5: Creating pm_user_token from JWT and testing...');
  
  // Decode JWT to get user info
  const parts = jwtToken.split('.');
  if (parts.length !== 3) {
    throw new Error('Invalid JWT format');
  }
  
  const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
  
  // Create pm_user_token using permission service logic
  const ssoUUID = crypto.randomUUID();
  const loginTime = Date.now();
  const userId = payload.sub;
  const sessionTimeout = 3600;
  
  const tokenString = `${ssoUUID}@${loginTime}.${userId}.${sessionTimeout}`;
  const pmUserToken = Buffer.from(tokenString).toString('base64');
  
  console.log(`   Created pm_user_token: ${pmUserToken.substring(0, 50)}...`);
  
  // Test with Order Service
  const testUrl = 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search';
  const testData = {
    storeIds: [],
    baseSize: 1,
    page: 1,
    size: 1,
    timestamp: Date.now()
  };
  
  try {
    const response = await fetch(testUrl, {
      method: 'POST',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Cookie': `test_orders-portal=${pmUserToken}`,
        'Origin': 'https://admin.ingka-dt.cn',
        'Referer': 'https://admin.ingka-dt.cn/',
        'User-Agent': 'MCP-JWT-Cookie-Converter/1.0.0',
        'X-Custom-Referrer': CONFIG.referer
      },
      body: JSON.stringify(testData)
    });
    
    console.log(`   Order Service response status: ${response.status}`);
    
    if (response.status === 200) {
      const responseData = await response.json();
      console.log(`   Response: ${JSON.stringify(responseData).substring(0, 100)}...`);
      
      if (responseData.code === 200 || responseData.data) {
        console.log('✅ pm_user_token works with Order Service!');
        return pmUserToken;
      } else if (responseData.code === 401) {
        console.log('⚠️ pm_user_token format recognized but needs proper session state');
        return pmUserToken;
      }
    }
    
    return pmUserToken;
    
  } catch (error) {
    console.log(`   Order Service test failed: ${error.message}`);
    return pmUserToken;
  }
}

/**
 * Save the cookie to file
 */
function saveCookie(cookie) {
  console.log('\n💾 Saving cookie to file...');
  
  const cookieData = {
    cookie: cookie,
    timestamp: new Date().toISOString(),
    expires: new Date(Date.now() + 3600 * 1000).toISOString(),
    domain: 'ingka-dt.cn',
    path: '/order-web',
    httpOnly: true,
    source: 'permission-service-real-api'
  };
  
  fs.writeFileSync(CONFIG.cookieOutputFile, JSON.stringify(cookieData, null, 2));
  
  console.log(`✅ Cookie saved to: ${CONFIG.cookieOutputFile}`);
  console.log(`   Cookie value: ${cookie.substring(0, 50)}...`);
}

/**
 * Main conversion function
 */
async function convertJWTToCookieRealAPI() {
  console.log('🎯 ==================== REAL PERMISSION SERVICE API CONVERSION ====================');
  console.log('🔧 Using actual permission service backend and BFF APIs\n');
  
  try {
    // Load JWT token
    const jwtToken = loadJWTToken();
    
    // Step 1: Try to get login URL from permission service backend
    try {
      const loginUrl = await getPermissionServiceLoginUrl(jwtToken);
      console.log(`   Login URL: ${loginUrl}`);
      
      // Step 3: Try to call BFF toLogin
      const redirectUrl = await callBFFToLogin(loginUrl, jwtToken);
      if (redirectUrl) {
        console.log(`   Got redirect URL: ${redirectUrl.substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`   ⚠️ Backend getLoginUrl failed: ${error.message}`);
    }
    
    // Step 2: Try to get user info
    const userInfo = await getUserInfoFromPermissionService(jwtToken);
    if (userInfo) {
      console.log('   User info retrieved successfully');
    }
    
    // Step 4: Test various endpoints
    await testPermissionServiceEndpoints(jwtToken);
    
    // Step 5: Create and test pm_user_token
    const pmUserToken = await createAndTestPmUserToken(jwtToken);
    saveCookie(pmUserToken);
    
    console.log('\n🎉 ==================== CONVERSION COMPLETE ====================');
    console.log('✅ Tested multiple approaches with real permission service APIs');
    console.log('📋 Results saved for analysis and further development');
    console.log('');
    console.log('💡 Next steps:');
    console.log('1. Check the generated cookie file for the pm_user_token');
    console.log('2. Analyze the API responses to understand authentication requirements');
    console.log('3. Consider integrating with permission service team for proper API access');
    
    return pmUserToken;
    
  } catch (error) {
    console.error('\n💥 ==================== CONVERSION FAILED ====================');
    console.error('❌ Real API conversion failed:', error.message);
    console.error('');
    console.error('🔧 This is expected - we need proper client credentials and API access');
    console.error('   Contact the permission service team for proper integration');
    process.exit(1);
  }
}

// Run the conversion if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  convertJWTToCookieRealAPI();
}

export { convertJWTToCookieRealAPI };
