# 🍪 Automated Cookie Extraction

This document explains how to use the automated cookie extraction system to obtain the `test_orders-portal` cookie for production API access.

## Overview

The automated cookie extraction system follows the complete OAuth2 authentication flow:

1. **Keycloak Authentication** - Uses existing browser session cookies
2. **Permission Service Login** - Initiates the OAuth2 flow
3. **OAuth2 Authorization** - Follows redirects to get authorization code
4. **Token Exchange** - Exchanges code for `pm_user_token`
5. **Cookie Extraction** - Extracts the final `test_orders-portal` cookie

## Quick Start

### 1. Extract Keycloak Cookies

First, extract your Keycloak session cookies from the browser:

```bash
npm run test:extract-keycloak-cookies
```

Follow the interactive instructions to:
- Login to https://admin.ingka-dt.cn/
- Open Developer Tools → Network tab
- Find requests to `keycloak.ingka-dt.cn`
- Copy the Cookie header value
- Paste it when prompted

### 2. Test Automated Extraction

Run the automated cookie extraction test:

```bash
npm run test:automated-cookie
```

This will:
- Validate your Keycloak cookies
- Run the complete OAuth2 flow
- Extract the `test_orders-portal` cookie
- Test it with the orders API
- Show integration instructions

### 3. Use in Production

```javascript
import { getProductionCookie } from './auth/automated-cookie-extractor.js';

// Get cookie automatically
const cookieResult = await getProductionCookie();
if (cookieResult.success) {
  const headers = {
    'Cookie': cookieResult.cookie,
    'Content-Type': 'application/json',
    // other headers...
  };
  
  // Use headers in API calls
}
```

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# Keycloak session cookies (extracted from browser)
KEYCLOAK_COOKIES="AUTH_SESSION_ID=...; KEYCLOAK_IDENTITY=...; KEYCLOAK_SESSION=..."

# Optional: Static token fallback
STATIC_PM_USER_TOKEN="your-static-token"

# Service endpoints (defaults shown)
PERMISSION_SERVICE_BASE="https://api-dev-mpp-fe.ingka-dt.cn"
ORDERS_API_BASE="https://fe-dev-i.ingka-dt.cn/order-web"
KEYCLOAK_BASE_URL="https://keycloak.ingka-dt.cn/auth"
KEYCLOAK_REALM="master"
```

### Required Cookies

Your `KEYCLOAK_COOKIES` must contain:
- `AUTH_SESSION_ID` - Session identifier
- `KEYCLOAK_IDENTITY` - User identity token
- `KEYCLOAK_SESSION` - Session information

## Authentication Flow

### Step 1: Permission Service Login

```
GET /prm-auth/auth/toLogin?clientId=orders-portal&redirectUrl=...&state=...
→ 302 Redirect to Keycloak
```

### Step 2: Keycloak Authentication

```
GET /auth/realms/master/protocol/openid-connect/auth?...
Cookie: AUTH_SESSION_ID=...; KEYCLOAK_IDENTITY=...; ...
→ 302 Redirect to loginComplete
```

### Step 3: Login Completion

```
GET /prm-auth/auth/loginComplete?state=...&code=...
→ 302 Redirect with pm_user_token
```

### Step 4: Final Cookie Setting

```
GET /order-web/user/current?pm_user_token=...
→ Set-Cookie: test_orders-portal=...
```

## API Integration

### MCP Server Integration

The automated cookie extractor integrates with the existing production auth solution:

```javascript
import { authenticate } from './auth/production-auth-solution.js';

const authResult = await authenticate();
if (authResult.success) {
  // Use authResult.pmUserToken for API calls
  const cookie = `test_orders-portal=${authResult.pmUserToken}`;
}
```

### Direct API Usage

```javascript
import { getAutomatedSessionCookie } from './auth/automated-cookie-extractor.js';

const sessionCookie = await getAutomatedSessionCookie();
if (sessionCookie) {
  const response = await fetch('https://fe-dev-i.ingka-dt.cn/order-web/orders/search', {
    method: 'POST',
    headers: {
      'Cookie': sessionCookie,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      storeIds: [],
      baseSize: 10,
      page: 1,
      size: 10,
      timestamp: Date.now()
    })
  });
}
```

## Troubleshooting

### Common Issues

1. **Missing Keycloak Cookies**
   ```
   Error: Missing Keycloak session cookies
   ```
   - Solution: Run `npm run test:extract-keycloak-cookies`
   - Make sure you're logged into admin.ingka-dt.cn

2. **Expired Cookies**
   ```
   Error: Keycloak authentication failed
   ```
   - Solution: Extract fresh cookies from browser
   - Keycloak sessions typically expire after inactivity

3. **Invalid Cookie Format**
   ```
   Error: Cookie extracted but not accepted by orders API
   ```
   - Solution: Check that all required cookies are present
   - Verify you copied the complete Cookie header

### Debug Mode

Enable debug logging:

```bash
DEBUG=1 npm run test:automated-cookie
```

### Manual Fallback

If automated extraction fails, use manual browser extraction:

```bash
npm run test:extract-token
```

## Security Considerations

1. **Cookie Storage**: Store cookies securely in environment variables
2. **Expiration**: Cookies typically expire in 1 hour
3. **Refresh**: Implement automatic refresh in production
4. **Monitoring**: Monitor authentication success rates

## Production Deployment

### Automatic Refresh

```javascript
import { getProductionCookie } from './auth/automated-cookie-extractor.js';

class AuthManager {
  constructor() {
    this.cookie = null;
    this.expiresAt = null;
  }
  
  async getCookie() {
    if (this.cookie && this.expiresAt && new Date() < this.expiresAt) {
      return this.cookie;
    }
    
    const result = await getProductionCookie();
    if (result.success) {
      this.cookie = result.cookie;
      this.expiresAt = result.expiresAt;
      return this.cookie;
    }
    
    throw new Error('Authentication failed');
  }
}
```

### Health Checks

```javascript
import { testExtractedCookie } from './auth/automated-cookie-extractor.js';

async function healthCheck() {
  const cookie = await getCookie();
  const cookieValue = cookie.split('=')[1];
  const isHealthy = await testExtractedCookie(cookieValue);
  
  return {
    status: isHealthy ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString()
  };
}
```

## Testing

### Unit Tests

```bash
npm run test:automated-cookie
```

### Integration Tests

```bash
npm run test:production-auth
```

### Manual Testing

```bash
# Test with curl
curl 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search' \
  -H 'content-type: application/json' \
  -b 'test_orders-portal=YOUR_COOKIE_VALUE' \
  --data-raw '{"storeIds":[],"baseSize":10,"page":1,"size":10,"timestamp":1754019228626}'
```

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Run debug mode for detailed logs
3. Verify your Keycloak session is active
4. Try manual browser extraction as fallback
