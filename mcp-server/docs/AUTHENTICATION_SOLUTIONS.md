# 🔐 Authentication Solutions for Orders Portal MCP Server

This document outlines all available authentication methods to obtain the `test_orders-portal` cookie for production API access.

## Overview

The MCP server needs to authenticate with the orders API using a `test_orders-portal` cookie. This cookie contains a Base64-encoded `pm_user_token` that the permission service validates.

## Available Authentication Methods

### 1. 🎯 Permission Service Client Credentials (Recommended)

**Best for**: Production environments, automated systems, CI/CD

Uses `permission-service` client credentials to authenticate directly with Keycloak and create valid `pm_user_token`.

#### Setup:
```bash
# Required environment variables
PERMISSION_SERVICE_CLIENT_ID=permission-service
PERMISSION_SERVICE_CLIENT_SECRET=your-client-secret

# Optional (defaults shown)
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn/auth
KEYCLOAK_REALM=master
PERMISSION_SERVICE_BASE=https://api-dev-mpp-fe.ingka-dt.cn
ORDERS_API_BASE=https://fe-dev-i.ingka-dt.cn/order-web
```

#### Test:
```bash
npm run test:permission-service-auth
```

#### Usage:
```javascript
import { getPermissionServiceSessionCookie } from './auth/permission-service-auth.js';

const sessionCookie = await getPermissionServiceSessionCookie();
// Returns: "test_orders-portal=base64-encoded-token"
```

#### Advantages:
- ✅ No browser interaction required
- ✅ Works in headless environments
- ✅ Service-to-service authentication
- ✅ Automatic token refresh possible
- ✅ Production-ready

#### Requirements:
- Permission service client credentials
- Client must have proper Keycloak permissions

---

### 2. 🍪 Automated Cookie Extraction

**Best for**: Development environments with browser access

Automates the complete OAuth2 flow using existing browser session cookies.

#### Setup:
```bash
# Extract Keycloak cookies from browser
npm run test:extract-keycloak-cookies

# This will save to .env:
KEYCLOAK_COOKIES="AUTH_SESSION_ID=...; KEYCLOAK_IDENTITY=...; ..."
```

#### Test:
```bash
npm run test:automated-cookie
```

#### Usage:
```javascript
import { getAutomatedSessionCookie } from './auth/automated-cookie-extractor.js';

const sessionCookie = await getAutomatedSessionCookie();
```

#### Advantages:
- ✅ Uses real user session
- ✅ Follows complete OAuth2 flow
- ✅ No additional client setup needed

#### Disadvantages:
- ❌ Requires browser session cookies
- ❌ Cookies expire with browser session
- ❌ Not suitable for headless environments

---

### 3. 🔄 Token Exchange (Existing JWT)

**Best for**: When you already have a valid JWT token

Uses existing `mcp-mcp-odi` JWT token to exchange for permission service token.

#### Setup:
```bash
# Ensure token file exists
ls mcp-mpc-odi.token.json

# Required environment variables
OAUTH2_CLIENT_ID=mcp-mcp-odi
OAUTH2_CLIENT_SECRET=your-mcp-client-secret
```

#### Test:
```bash
npm run test:complete-auth
```

#### Usage:
```javascript
import { authenticate } from './auth/production-auth-solution.js';

const result = await authenticate();
if (result.success) {
  const cookie = `test_orders-portal=${result.pmUserToken}`;
}
```

#### Advantages:
- ✅ Uses existing JWT infrastructure
- ✅ Token exchange is standard OAuth2

#### Disadvantages:
- ❌ Generated tokens may not be accepted by API
- ❌ Requires valid JWT token file

---

### 4. 📋 Static Token (Manual)

**Best for**: Quick testing, temporary solutions

Manually extract token from browser and set as environment variable.

#### Setup:
```bash
# Extract token manually from browser
npm run test:extract-token

# Set in .env
STATIC_PM_USER_TOKEN=your-extracted-token
```

#### Test:
```bash
TOKEN="your-token" npm run test:extract-token
```

#### Advantages:
- ✅ Simple and direct
- ✅ Works immediately
- ✅ Good for testing

#### Disadvantages:
- ❌ Manual process
- ❌ Tokens expire (usually 1 hour)
- ❌ Not suitable for production

---

## Production Authentication Strategy

The MCP server tries authentication methods in this order:

1. **Static Token** (if `STATIC_PM_USER_TOKEN` is set)
2. **Permission Service Auth** (if `PERMISSION_SERVICE_CLIENT_SECRET` is set)
3. **Automated Cookie Extraction** (if `KEYCLOAK_COOKIES` is set)
4. **Token Exchange** (if JWT token file exists)
5. **Fallback** (returns error with instructions)

### Integration Example:

```javascript
import { authenticate } from './auth/production-auth-solution.js';

// This will try all methods automatically
const authResult = await authenticate();

if (authResult.success) {
  console.log(`Authentication successful: ${authResult.method}`);
  console.log(`Token expires: ${authResult.expiresAt}`);
  
  // Use the token
  const headers = {
    'Cookie': `test_orders-portal=${authResult.pmUserToken}`,
    'Content-Type': 'application/json'
  };
} else {
  console.error(`Authentication failed: ${authResult.error}`);
}
```

## Recommended Setup for Different Environments

### Development Environment:
```bash
# Option 1: Use browser cookies (easiest)
npm run test:extract-keycloak-cookies
npm run test:automated-cookie

# Option 2: Use static token (quick testing)
npm run test:extract-token
```

### Production Environment:
```bash
# Recommended: Use permission service client credentials
PERMISSION_SERVICE_CLIENT_SECRET=your-secret
npm run test:permission-service-auth
```

### CI/CD Environment:
```bash
# Use service credentials or static token
PERMISSION_SERVICE_CLIENT_SECRET=your-secret
# OR
STATIC_PM_USER_TOKEN=your-token
```

## Troubleshooting

### Common Issues:

1. **"Missing client secret"**
   - Contact Keycloak administrator for credentials
   - Ensure client exists and has proper permissions

2. **"Token not accepted by API"**
   - Check token format and expiration
   - Verify API endpoint and headers

3. **"Keycloak authentication failed"**
   - Refresh browser session
   - Extract fresh cookies

4. **"Permission denied"**
   - Check client permissions in Keycloak
   - Verify user has access to orders portal

### Debug Mode:
```bash
DEBUG=1 npm run test:permission-service-auth
```

### Health Check:
```bash
# Test current authentication status
npm run test:production-auth
```

## Security Considerations

1. **Client Secrets**: Store securely, never commit to version control
2. **Token Expiration**: Implement automatic refresh in production
3. **Network Security**: Use HTTPS for all communications
4. **Monitoring**: Log authentication success/failure rates
5. **Fallback**: Have multiple authentication methods available

## Getting Client Credentials

To get `permission-service` client credentials:

1. Contact your Keycloak administrator
2. Request access to `permission-service` client
3. Ensure client has these permissions:
   - Token exchange
   - Service account roles
   - Access to orders-portal audience
4. Get client secret and configure in environment

## Next Steps

1. Choose the authentication method that fits your environment
2. Configure the required environment variables
3. Test the authentication with the provided test scripts
4. Integrate with your MCP server
5. Implement automatic token refresh for production use
