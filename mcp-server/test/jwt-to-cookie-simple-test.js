#!/usr/bin/env node

/**
 * 🎯 Simple JWT to Cookie Test
 *
 * Purpose:
 * 1. Test simple JWT to pm_user_token conversion
 * 2. Use existing mcp-mpc-odi.token.json file
 * 3. Create working cookie without additional client credentials
 */

import { 
  convertJWTToCookie,
  getJWTBasedSessionCookie,
  loadJWTToken,
  loadJWTToCookieConfig,
  createPmUserTokenFromJWT,
  testJWTBasedPmToken
} from '../dist/auth/jwt-to-cookie-simple.js';

/**
 * Show configuration and token status
 */
function showStatus() {
  console.log('📋 ==================== STATUS CHECK ====================');
  
  const config = loadJWTToCookieConfig();
  const jwtResult = loadJWTToken();
  
  console.log('🔧 Configuration:');
  console.log(`   Permission Service: ${config.permissionServiceBase}`);
  console.log(`   Orders API: ${config.ordersApiBase}`);
  console.log(`   Client ID: ${config.clientId}`);
  console.log(`   Redirect URL: ${config.redirectUrl}`);
  console.log('');
  
  console.log('🎫 JWT Token Status:');
  console.log(`   Token File: ${jwtResult.success ? 'Found ✅' : 'Missing ❌'}`);
  
  if (jwtResult.success && jwtResult.userInfo) {
    console.log(`   User: ${jwtResult.userInfo.name} (${jwtResult.userInfo.email})`);
    console.log(`   User ID: ${jwtResult.userInfo.userId}`);
    console.log(`   Session ID: ${jwtResult.userInfo.sessionId}`);
    console.log(`   Expires: ${jwtResult.userInfo.expiresAt.toISOString()}`);
    
    const isExpired = new Date() > jwtResult.userInfo.expiresAt;
    console.log(`   Status: ${isExpired ? 'Expired ❌' : 'Valid ✅'}`);
    
    if (isExpired) {
      console.log('   ⚠️ JWT token is expired - you may need to refresh it');
    }
  }
  
  console.log('');
  
  const canProceed = jwtResult.success && jwtResult.userInfo && new Date() < jwtResult.userInfo.expiresAt;
  console.log(`🎯 Ready for conversion: ${canProceed ? 'Yes ✅' : 'No ❌'}`);
  
  if (!canProceed) {
    console.log('');
    console.log('⚠️ **Cannot proceed:**');
    if (!jwtResult.success) {
      console.log('   • JWT token file (mcp-mpc-odi.token.json) not found or invalid');
      console.log('   • Make sure you have a valid JWT token file in the project root');
    } else if (new Date() > jwtResult.userInfo.expiresAt) {
      console.log('   • JWT token is expired');
      console.log('   • You need to refresh your JWT token');
    }
    console.log('');
    console.log('📋 **How to get a valid JWT token:**');
    console.log('   1. Login to admin.ingka-dt.cn in your browser');
    console.log('   2. Use browser developer tools to extract a fresh JWT token');
    console.log('   3. Update the mcp-mpc-odi.token.json file');
  }
  
  return canProceed;
}

/**
 * Test individual steps
 */
async function testConversionSteps() {
  console.log('\n🔄 ==================== STEP-BY-STEP CONVERSION TEST ====================');
  
  const jwtResult = loadJWTToken();
  const config = loadJWTToCookieConfig();
  
  if (!jwtResult.success) {
    console.log('❌ Cannot test steps without valid JWT token');
    return null;
  }
  
  try {
    // Step 1: Create pm_user_token from JWT
    console.log('🔄 Step 1: Creating pm_user_token from JWT user info...');
    const pmTokenResult = createPmUserTokenFromJWT(jwtResult.userInfo);
    
    if (!pmTokenResult.success) {
      console.log('❌ Step 1 failed: Could not create pm_user_token');
      return null;
    }
    
    console.log('✅ Step 1 successful: PM user token created');
    console.log(`   Token: ${pmTokenResult.pmUserToken.substring(0, 50)}...`);
    console.log(`   Expires: ${pmTokenResult.expiresAt.toISOString()}`);
    
    // Show decoded token info
    try {
      const tokenString = Buffer.from(pmTokenResult.pmUserToken, 'base64').toString();
      console.log(`   Decoded: ${tokenString}`);
      
      const parts = tokenString.split('@');
      if (parts.length === 2) {
        const ssoUUID = parts[0];
        const rightPart = parts[1];
        const rightParts = rightPart.split('.');
        
        if (rightParts.length >= 3) {
          const loginTime = rightParts[0];
          const userId = rightParts[1];
          const sessionTimeout = rightParts[rightParts.length - 1];
          
          console.log('   Token Breakdown:');
          console.log(`     SSO UUID: ${ssoUUID}`);
          console.log(`     Login Time: ${new Date(parseInt(loginTime)).toISOString()}`);
          console.log(`     User ID: ${userId}`);
          console.log(`     Session Timeout: ${sessionTimeout}s`);
        }
      }
    } catch (decodeError) {
      console.log('   Could not decode token details');
    }
    
    // Step 2: Test with API
    console.log('\n🔄 Step 2: Testing pm_user_token with orders API...');
    const apiTest = await testJWTBasedPmToken(pmTokenResult.pmUserToken, config);
    
    if (!apiTest) {
      console.log('❌ Step 2 failed: PM user token not accepted by API');
      return {
        tokenCreated: true,
        apiWorking: false,
        pmUserToken: pmTokenResult.pmUserToken
      };
    }
    
    console.log('✅ Step 2 successful: API accepts PM user token');
    
    return {
      tokenCreated: true,
      apiWorking: true,
      pmUserToken: pmTokenResult.pmUserToken,
      expiresAt: pmTokenResult.expiresAt
    };
    
  } catch (error) {
    console.error('❌ Step-by-step test failed:', error);
    return null;
  }
}

/**
 * Test complete conversion
 */
async function testCompleteConversion() {
  console.log('\n🚀 ==================== COMPLETE CONVERSION TEST ====================');
  
  try {
    console.log('🔄 Running complete JWT to cookie conversion...');
    
    const result = await convertJWTToCookie();
    
    console.log('📊 Conversion Results:');
    console.log(`   Success: ${result.success ? 'Yes ✅' : 'No ❌'}`);
    console.log(`   Method: ${result.method}`);
    
    if (result.success) {
      console.log(`   PM User Token: ${result.pmUserToken?.substring(0, 50)}...`);
      console.log(`   Cookie Value: ${result.cookieValue?.substring(0, 50)}...`);
      console.log(`   Expires At: ${result.expiresAt?.toISOString() || 'Unknown'}`);
      
      return result;
    } else {
      console.log(`   Error: ${result.error}`);
      return null;
    }
    
  } catch (error) {
    console.error('❌ Complete conversion test failed:', error);
    return null;
  }
}

/**
 * Test session cookie integration
 */
async function testSessionCookieIntegration() {
  console.log('\n🍪 ==================== SESSION COOKIE INTEGRATION TEST ====================');
  
  try {
    console.log('🔄 Testing session cookie integration...');
    
    const sessionCookie = await getJWTBasedSessionCookie();
    
    if (sessionCookie) {
      console.log('✅ Session cookie obtained successfully');
      console.log(`   Cookie: ${sessionCookie.substring(0, 80)}...`);
      
      // Test the cookie with a direct API call
      console.log('🔄 Testing cookie with direct API call...');
      
      const cookieValue = sessionCookie.split('=')[1];
      const config = loadJWTToCookieConfig();
      const apiTest = await testJWTBasedPmToken(cookieValue, config);
      
      console.log(`   API Test: ${apiTest ? 'Passed ✅' : 'Failed ❌'}`);
      
      if (apiTest) {
        console.log('🎉 **SUCCESS! Session cookie is working!**');
        return sessionCookie;
      }
    } else {
      console.log('❌ Failed to obtain session cookie');
    }
    
    return null;
    
  } catch (error) {
    console.error('❌ Session cookie integration test failed:', error);
    return null;
  }
}

/**
 * Show usage instructions
 */
function showUsageInstructions(workingCookie) {
  console.log('\n🔧 ==================== USAGE INSTRUCTIONS ====================');
  
  if (workingCookie) {
    console.log('✅ **Your JWT to cookie conversion is working!**');
    console.log('');
    console.log('📋 **For MCP Server Integration:**');
    console.log('```javascript');
    console.log('import { getJWTBasedSessionCookie } from "./auth/jwt-to-cookie-simple.js";');
    console.log('');
    console.log('// Get cookie from JWT token');
    console.log('const sessionCookie = await getJWTBasedSessionCookie();');
    console.log('if (sessionCookie) {');
    console.log('  const headers = {');
    console.log('    "Cookie": sessionCookie,');
    console.log('    // other headers...');
    console.log('  };');
    console.log('}');
    console.log('```');
    console.log('');
    console.log('📋 **For Direct API Calls:**');
    console.log('```bash');
    const cookieValue = workingCookie.split('=')[1];
    console.log(`curl 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search' \\`);
    console.log(`  -H 'content-type: application/json' \\`);
    console.log(`  -b '${workingCookie}' \\`);
    console.log(`  --data-raw '{"storeIds":[],"baseSize":10,"page":1,"size":10,"timestamp":${Date.now()}}'`);
    console.log('```');
    console.log('');
    console.log('📋 **Environment Variable (Alternative):**');
    console.log(`export STATIC_PM_USER_TOKEN="${cookieValue}"`);
    
  } else {
    console.log('❌ **JWT to cookie conversion failed**');
    console.log('');
    console.log('🔧 **Troubleshooting Steps:**');
    console.log('   1. Ensure mcp-mpc-odi.token.json file exists and is valid');
    console.log('   2. Check that JWT token is not expired');
    console.log('   3. Verify network connectivity to permission service');
    console.log('   4. Try refreshing your JWT token from browser');
    console.log('');
    console.log('📋 **Alternative Methods:**');
    console.log('   • Use permission service client credentials (recommended)');
    console.log('   • Extract cookie manually from browser');
    console.log('   • Use automated cookie extraction with browser cookies');
  }
  
  console.log('');
  console.log('⏰ **Important Notes:**');
  console.log('   • Tokens expire with the original JWT (usually 1 hour)');
  console.log('   • This method uses existing JWT token information');
  console.log('   • No additional client credentials required');
  console.log('   • Good for development and testing');
}

/**
 * Main test function
 */
async function runJWTToCookieTest() {
  console.log('🎯 ==================== JWT TO COOKIE CONVERSION TEST ====================');
  console.log('🎯 Purpose: Convert existing JWT token to pm_user_token cookie\n');
  
  // Step 1: Check status
  const canProceed = showStatus();
  
  if (!canProceed) {
    console.log('\n❌ Cannot proceed without valid JWT token');
    console.log('   Please follow the instructions above to get a valid JWT token');
    return;
  }
  
  // Step 2: Test individual steps
  const stepResults = await testConversionSteps();
  
  // Step 3: Test complete conversion
  const completeResult = await testCompleteConversion();
  
  // Step 4: Test session cookie integration
  const sessionCookie = await testSessionCookieIntegration();
  
  // Step 5: Show usage instructions
  showUsageInstructions(sessionCookie);
  
  console.log('\n🏁 ==================== TEST COMPLETE ====================');
  console.log('✅ JWT to cookie conversion test finished');
  console.log('');
  console.log('🎯 **Final Results:**');
  console.log(`   JWT Token: ${canProceed ? 'Valid ✅' : 'Invalid ❌'}`);
  console.log(`   Token Creation: ${stepResults?.tokenCreated ? 'Success ✅' : 'Failed ❌'}`);
  console.log(`   API Integration: ${stepResults?.apiWorking ? 'Success ✅' : 'Failed ❌'}`);
  console.log(`   Complete Flow: ${completeResult ? 'Success ✅' : 'Failed ❌'}`);
  console.log(`   Session Cookie: ${sessionCookie ? 'Working ✅' : 'Failed ❌'}`);
  console.log('');
  
  if (sessionCookie) {
    console.log('🎉 **PERFECT! JWT to cookie conversion is working!**');
    console.log('   Your MCP server can now use existing JWT tokens for authentication!');
  } else {
    console.log('⚠️ **Needs attention - check JWT token and try again**');
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runJWTToCookieTest().catch(error => {
    console.error('💥 JWT to cookie conversion test failed:', error);
    process.exit(1);
  });
}

export { runJWTToCookieTest };
