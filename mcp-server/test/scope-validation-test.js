#!/usr/bin/env node

/**
 * 🔍 Scope Validation Test
 *
 * Purpose:
 * 1. Test OAuth2 scope validation during connection
 * 2. Debug infinite retry issues with MCP Inspector
 * 3. Verify proper error responses for scope mismatches
 */

import { validateOAuth2Connection, loadConnectionAuthConfig } from '../dist/auth/connection-auth.js';

/**
 * Mock request object for testing
 */
function createMockRequest(token) {
  return {
    headers: {
      authorization: token ? `Bearer ${token}` : undefined,
      'user-agent': 'MCP-Scope-Test/1.0.0'
    },
    method: 'POST',
    path: '/mcp',
    body: { method: 'initialize' }
  };
}

/**
 * Mock OAuth2 provider with configurable scopes
 */
function createMockProvider(userScopes, clientId = 'permission-service') {
  return {
    verifyAccessToken: async (token) => {
      if (token === 'valid-token') {
        return {
          token,
          clientId,
          scopes: userScopes,
          expiresAt: Math.floor(Date.now() / 1000) + 3600,
          extra: {
            userInfo: {
              sub: 'test-user',
              azp: clientId,
              exp: Math.floor(Date.now() / 1000) + 3600
            },
            introspectionData: {
              active: true,
              client_id: clientId,
              scope: userScopes.join(' ')
            }
          }
        };
      }
      throw new Error('Invalid token');
    }
  };
}

/**
 * Test scope validation scenarios
 */
async function testScopeValidation() {
  console.log('🔍 ==================== SCOPE VALIDATION TEST ====================');
  
  const config = loadConnectionAuthConfig();
  console.log('📋 Current Configuration:');
  console.log(`   Required Scopes: [${config.requiredScopes?.join(', ') || 'none'}]`);
  console.log(`   Skip Scope Validation: ${config.skipScopeValidation ? 'Yes ⚠️' : 'No ✅'}`);
  console.log(`   Allowed Clients: [${config.allowedClients?.join(', ') || 'any'}]`);
  console.log('');

  const testCases = [
    {
      name: 'Valid Scopes (profile, roles, email)',
      scopes: ['profile', 'roles', 'email'],
      expectedSuccess: true
    },
    {
      name: 'Missing Required Scope (only roles, email)',
      scopes: ['roles', 'email'],
      expectedSuccess: config.skipScopeValidation // Should succeed only if validation is skipped
    },
    {
      name: 'No Scopes',
      scopes: [],
      expectedSuccess: config.skipScopeValidation // Should succeed only if validation is skipped
    },
    {
      name: 'Different Scopes (read, write)',
      scopes: ['read', 'write'],
      expectedSuccess: config.skipScopeValidation // Should succeed only if validation is skipped
    },
    {
      name: 'OpenID Scope Only',
      scopes: ['openid'],
      expectedSuccess: config.skipScopeValidation // Should succeed only if validation is skipped
    }
  ];

  const results = [];

  for (const testCase of testCases) {
    console.log(`🔄 Testing: ${testCase.name}`);
    console.log(`   Scopes: [${testCase.scopes.join(', ')}]`);
    
    try {
      const provider = createMockProvider(testCase.scopes);
      const request = createMockRequest('valid-token');
      
      const result = await validateOAuth2Connection(request, provider);
      
      console.log(`   Result: ${result.success ? 'Success ✅' : 'Failed ❌'}`);
      if (!result.success) {
        console.log(`   Error: ${result.error}`);
      }
      
      const passed = result.success === testCase.expectedSuccess;
      console.log(`   Expected: ${testCase.expectedSuccess ? 'Success' : 'Failure'} - ${passed ? 'PASS ✅' : 'FAIL ❌'}`);
      
      results.push({
        name: testCase.name,
        success: result.success,
        expected: testCase.expectedSuccess,
        passed,
        error: result.error
      });
      
    } catch (error) {
      console.log(`   Exception: ${error.message} ❌`);
      results.push({
        name: testCase.name,
        success: false,
        expected: testCase.expectedSuccess,
        passed: false,
        error: error.message
      });
    }
    
    console.log('');
  }

  return results;
}

/**
 * Test HTTP error response format
 */
async function testErrorResponseFormat() {
  console.log('📡 ==================== ERROR RESPONSE FORMAT TEST ====================');
  
  console.log('🔄 Testing scope validation error response format...');
  
  try {
    const provider = createMockProvider(['roles', 'email']); // Missing 'profile'
    const request = createMockRequest('valid-token');
    
    const result = await validateOAuth2Connection(request, provider);
    
    if (!result.success) {
      console.log('📊 Error Response Analysis:');
      console.log(`   Error Message: "${result.error}"`);
      console.log(`   Message Length: ${result.error.length} characters`);
      console.log(`   Contains Scope Info: ${result.error.includes('Required:') ? 'Yes ✅' : 'No ❌'}`);
      console.log(`   Contains Provided Info: ${result.error.includes('Provided:') ? 'Yes ✅' : 'No ❌'}`);
      console.log(`   Contains Missing Info: ${result.error.includes('Missing:') ? 'Yes ✅' : 'No ❌'}`);
      
      // Simulate HTTP response
      const httpResponse = {
        status: 401,
        headers: {
          'WWW-Authenticate': `Bearer realm="MCP Server", error="invalid_token", error_description="${result.error}"`
        },
        body: {
          error: 'invalid_token',
          error_description: result.error,
          error_uri: 'https://tools.ietf.org/html/rfc6750#section-3.1'
        }
      };
      
      console.log('');
      console.log('📡 Simulated HTTP Response:');
      console.log(`   Status: ${httpResponse.status}`);
      console.log(`   WWW-Authenticate: ${httpResponse.headers['WWW-Authenticate']}`);
      console.log(`   Body: ${JSON.stringify(httpResponse.body, null, 2)}`);
      
      return {
        hasError: true,
        errorMessage: result.error,
        httpResponse
      };
    } else {
      console.log('⚠️ Expected error but got success (scope validation might be disabled)');
      return {
        hasError: false,
        errorMessage: null,
        httpResponse: null
      };
    }
    
  } catch (error) {
    console.log(`❌ Test failed with exception: ${error.message}`);
    return {
      hasError: true,
      errorMessage: error.message,
      httpResponse: null
    };
  }
}

/**
 * Provide recommendations for fixing infinite retry issue
 */
function provideRecommendations(scopeResults, errorResponse) {
  console.log('💡 ==================== RECOMMENDATIONS ====================');
  
  const config = loadConnectionAuthConfig();
  
  if (config.skipScopeValidation) {
    console.log('⚠️ **Scope validation is currently disabled**');
    console.log('   This might mask the infinite retry issue');
    console.log('   Set CONNECTION_AUTH_SKIP_SCOPE_VALIDATION=false to test properly');
    console.log('');
  }
  
  console.log('🔧 **To fix infinite retry issue:**');
  console.log('');
  
  console.log('1. **Check OAuth2 Scopes Configuration**');
  console.log('   Current OAUTH2_SCOPES in .env: profile,roles,email');
  console.log('   Required scopes for connection: profile');
  console.log('   ✅ These should match - verify MCP Inspector uses same scopes');
  console.log('');
  
  console.log('2. **Verify HTTP Error Response Format**');
  if (errorResponse.hasError) {
    console.log('   ✅ Error response includes detailed scope information');
    console.log('   ✅ WWW-Authenticate header is properly formatted');
    console.log('   ✅ OAuth2 standard error format is used');
  } else {
    console.log('   ⚠️ No error response generated (validation might be disabled)');
  }
  console.log('');
  
  console.log('3. **MCP Inspector Configuration**');
  console.log('   • Ensure MCP Inspector requests scopes: profile,roles,email');
  console.log('   • Check if MCP Inspector handles 401 responses properly');
  console.log('   • Verify MCP Inspector doesn\'t retry on authentication errors');
  console.log('   • Consider using CONNECTION_AUTH_SKIP_SCOPE_VALIDATION=true for testing');
  console.log('');
  
  console.log('4. **Debugging Steps**');
  console.log('   • Enable detailed logging in MCP Inspector');
  console.log('   • Check MCP Server logs for repeated authentication attempts');
  console.log('   • Verify OAuth2 token contains expected scopes');
  console.log('   • Test with curl to isolate MCP Inspector behavior');
  console.log('');
  
  console.log('5. **Quick Fix for Testing**');
  console.log('   Set CONNECTION_AUTH_SKIP_SCOPE_VALIDATION=true in .env');
  console.log('   This will bypass scope validation while keeping other auth checks');
  console.log('');
  
  const failedTests = scopeResults.filter(r => !r.passed);
  if (failedTests.length > 0) {
    console.log('❌ **Failed Test Cases:**');
    failedTests.forEach(test => {
      console.log(`   • ${test.name}: ${test.error}`);
    });
  } else {
    console.log('✅ **All scope validation tests passed**');
  }
}

/**
 * Main test function
 */
async function runScopeValidationTest() {
  console.log('🔍 ==================== SCOPE VALIDATION DEBUG TEST ====================');
  console.log('🎯 Purpose: Debug infinite retry issue with MCP Inspector\n');
  
  // Step 1: Test scope validation
  const scopeResults = await testScopeValidation();
  
  // Step 2: Test error response format
  const errorResponse = await testErrorResponseFormat();
  
  // Step 3: Provide recommendations
  provideRecommendations(scopeResults, errorResponse);
  
  console.log('\n🏁 ==================== TEST COMPLETE ====================');
  
  const passedTests = scopeResults.filter(r => r.passed).length;
  const totalTests = scopeResults.length;
  
  console.log(`✅ Scope Tests: ${passedTests}/${totalTests} passed`);
  console.log(`📡 Error Response: ${errorResponse.hasError ? 'Generated ✅' : 'Not Generated ⚠️'}`);
  
  if (passedTests === totalTests && errorResponse.hasError) {
    console.log('🎉 **Scope validation is working correctly**');
    console.log('   The infinite retry issue might be in MCP Inspector configuration');
  } else {
    console.log('⚠️ **Issues detected in scope validation**');
    console.log('   Review the recommendations above');
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runScopeValidationTest().catch(error => {
    console.error('💥 Scope validation test failed:', error);
    process.exit(1);
  });
}

export { runScopeValidationTest };
