#!/usr/bin/env node

/**
 * 🔐 Permission Service Authentication Test
 *
 * Purpose:
 * 1. Test permission-service client credentials authentication
 * 2. Demonstrate how to get pm_user_token using service credentials
 * 3. Provide production-ready authentication without browser cookies
 */

import { 
  authenticateWithPermissionService,
  getPermissionServiceSessionCookie,
  loadPermissionServiceAuthConfig,
  getPermissionServiceToken,
  createPmUserTokenFromServiceAuth,
  testPermissionServicePmToken
} from '../dist/auth/permission-service-auth.js';

/**
 * Show configuration status
 */
function showConfigurationStatus() {
  console.log('📋 ==================== CONFIGURATION STATUS ====================');
  
  const config = loadPermissionServiceAuthConfig();
  
  console.log('🔧 Configuration:');
  console.log(`   Keycloak Base URL: ${config.keycloakBaseUrl}`);
  console.log(`   Keycloak Realm: ${config.keycloakRealm}`);
  console.log(`   Permission Service: ${config.permissionServiceBase}`);
  console.log(`   Orders API: ${config.ordersApiBase}`);
  console.log(`   Client ID: ${config.clientId}`);
  console.log(`   Client Secret: ${config.clientSecret ? 'Configured ✅' : 'Missing ❌'}`);
  console.log(`   Target Client: ${config.targetClientId}`);
  console.log('');
  
  const hasRequiredConfig = config.clientSecret;
  console.log(`🎯 Ready for authentication: ${hasRequiredConfig ? 'Yes ✅' : 'No ❌'}`);
  
  if (!hasRequiredConfig) {
    console.log('');
    console.log('⚠️ **Missing Configuration:**');
    console.log('   You need permission-service client credentials to proceed.');
    console.log('   Set: PERMISSION_SERVICE_CLIENT_SECRET="your-client-secret"');
    console.log('   Optional: PERMISSION_SERVICE_CLIENT_ID="permission-service" (default)');
    console.log('');
    console.log('📋 **How to get client credentials:**');
    console.log('   1. Contact your Keycloak administrator');
    console.log('   2. Request permission-service client credentials');
    console.log('   3. Ensure the client has proper permissions for token exchange');
    console.log('   4. Set the credentials in your .env file');
  }
  
  return hasRequiredConfig;
}

/**
 * Test individual steps of the authentication process
 */
async function testAuthenticationSteps() {
  console.log('\n🔄 ==================== STEP-BY-STEP AUTHENTICATION TEST ====================');
  
  const config = loadPermissionServiceAuthConfig();
  
  try {
    // Step 1: Test token acquisition
    console.log('🔄 Step 1: Testing permission-service token acquisition...');
    const tokenResult = await getPermissionServiceToken(config);
    
    if (!tokenResult.success) {
      console.log('❌ Step 1 failed: Could not get permission-service token');
      return null;
    }
    
    console.log('✅ Step 1 successful: Permission-service token obtained');
    console.log(`   Token preview: ${tokenResult.accessToken.substring(0, 50)}...`);
    console.log(`   User info: ${JSON.stringify(tokenResult.userInfo, null, 2)}`);
    
    // Step 2: Test pm_user_token creation
    console.log('\n🔄 Step 2: Testing pm_user_token creation...');
    const pmTokenResult = createPmUserTokenFromServiceAuth(tokenResult.userInfo, config);
    
    if (!pmTokenResult.success) {
      console.log('❌ Step 2 failed: Could not create pm_user_token');
      return null;
    }
    
    console.log('✅ Step 2 successful: PM user token created');
    console.log(`   Token: ${pmTokenResult.pmUserToken.substring(0, 50)}...`);
    console.log(`   Expires: ${pmTokenResult.expiresAt.toISOString()}`);
    
    // Step 3: Test API integration
    console.log('\n🔄 Step 3: Testing API integration...');
    const apiTest = await testPermissionServicePmToken(pmTokenResult.pmUserToken, config);
    
    if (!apiTest) {
      console.log('❌ Step 3 failed: PM user token not accepted by API');
      return {
        tokenObtained: true,
        pmTokenCreated: true,
        apiWorking: false,
        pmUserToken: pmTokenResult.pmUserToken
      };
    }
    
    console.log('✅ Step 3 successful: API accepts PM user token');
    
    return {
      tokenObtained: true,
      pmTokenCreated: true,
      apiWorking: true,
      pmUserToken: pmTokenResult.pmUserToken,
      expiresAt: pmTokenResult.expiresAt
    };
    
  } catch (error) {
    console.error('❌ Step-by-step test failed:', error);
    return null;
  }
}

/**
 * Test complete authentication flow
 */
async function testCompleteAuthentication() {
  console.log('\n🚀 ==================== COMPLETE AUTHENTICATION TEST ====================');
  
  try {
    console.log('🔄 Running complete permission service authentication...');
    
    const result = await authenticateWithPermissionService();
    
    console.log('📊 Authentication Results:');
    console.log(`   Success: ${result.success ? 'Yes ✅' : 'No ❌'}`);
    console.log(`   Method: ${result.method}`);
    
    if (result.success) {
      console.log(`   PM User Token: ${result.pmUserToken?.substring(0, 50)}...`);
      console.log(`   Cookie Value: ${result.cookieValue?.substring(0, 50)}...`);
      console.log(`   Expires At: ${result.expiresAt?.toISOString() || 'Unknown'}`);
      
      if (result.userInfo) {
        console.log(`   User Info: ${JSON.stringify(result.userInfo, null, 2)}`);
      }
      
      // Show decoded token info
      if (result.cookieValue) {
        try {
          const tokenString = Buffer.from(result.cookieValue, 'base64').toString();
          console.log(`   Decoded Token: ${tokenString}`);
          
          const parts = tokenString.split('@');
          if (parts.length === 2) {
            const ssoUUID = parts[0];
            const rightPart = parts[1];
            const rightParts = rightPart.split('.');
            
            if (rightParts.length >= 3) {
              const loginTime = rightParts[0];
              const userId = rightParts[1];
              const sessionTimeout = rightParts[rightParts.length - 1];
              
              console.log('   Token Details:');
              console.log(`     SSO UUID: ${ssoUUID}`);
              console.log(`     Login Time: ${new Date(parseInt(loginTime)).toISOString()}`);
              console.log(`     User ID: ${userId}`);
              console.log(`     Session Timeout: ${sessionTimeout}s`);
            }
          }
        } catch (decodeError) {
          console.log('   Could not decode token details');
        }
      }
      
      return result;
    } else {
      console.log(`   Error: ${result.error}`);
      return null;
    }
    
  } catch (error) {
    console.error('❌ Complete authentication test failed:', error);
    return null;
  }
}

/**
 * Test session cookie integration
 */
async function testSessionCookieIntegration() {
  console.log('\n🍪 ==================== SESSION COOKIE INTEGRATION TEST ====================');
  
  try {
    console.log('🔄 Testing session cookie integration...');
    
    const sessionCookie = await getPermissionServiceSessionCookie();
    
    if (sessionCookie) {
      console.log('✅ Session cookie obtained successfully');
      console.log(`   Cookie: ${sessionCookie.substring(0, 80)}...`);
      
      // Test the cookie with a direct API call
      console.log('🔄 Testing cookie with direct API call...');
      
      const cookieValue = sessionCookie.split('=')[1];
      const config = loadPermissionServiceAuthConfig();
      const apiTest = await testPermissionServicePmToken(cookieValue, config);
      
      console.log(`   API Test: ${apiTest ? 'Passed ✅' : 'Failed ❌'}`);
      
      if (apiTest) {
        console.log('🎉 **SUCCESS! Session cookie is working!**');
        return sessionCookie;
      }
    } else {
      console.log('❌ Failed to obtain session cookie');
    }
    
    return null;
    
  } catch (error) {
    console.error('❌ Session cookie integration test failed:', error);
    return null;
  }
}

/**
 * Show usage instructions
 */
function showUsageInstructions(workingCookie) {
  console.log('\n🔧 ==================== USAGE INSTRUCTIONS ====================');
  
  if (workingCookie) {
    console.log('✅ **Your permission service authentication is working!**');
    console.log('');
    console.log('📋 **For MCP Server Integration:**');
    console.log('```javascript');
    console.log('import { getPermissionServiceSessionCookie } from "./auth/permission-service-auth.js";');
    console.log('');
    console.log('// Get cookie automatically');
    console.log('const sessionCookie = await getPermissionServiceSessionCookie();');
    console.log('if (sessionCookie) {');
    console.log('  const headers = {');
    console.log('    "Cookie": sessionCookie,');
    console.log('    // other headers...');
    console.log('  };');
    console.log('}');
    console.log('```');
    console.log('');
    console.log('📋 **For Direct API Calls:**');
    console.log('```bash');
    const cookieValue = workingCookie.split('=')[1];
    console.log(`curl 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search' \\`);
    console.log(`  -H 'content-type: application/json' \\`);
    console.log(`  -b '${workingCookie}' \\`);
    console.log(`  --data-raw '{"storeIds":[],"baseSize":10,"page":1,"size":10,"timestamp":${Date.now()}}'`);
    console.log('```');
    console.log('');
    console.log('📋 **Environment Variable (Alternative):**');
    console.log(`export STATIC_PM_USER_TOKEN="${cookieValue}"`);
    
  } else {
    console.log('❌ **Permission service authentication failed**');
    console.log('');
    console.log('🔧 **Troubleshooting Steps:**');
    console.log('   1. Verify PERMISSION_SERVICE_CLIENT_SECRET is correct');
    console.log('   2. Check that permission-service client exists in Keycloak');
    console.log('   3. Ensure client has proper permissions for token exchange');
    console.log('   4. Verify network connectivity to Keycloak and permission service');
    console.log('');
    console.log('📋 **Configuration Check:**');
    console.log('   • PERMISSION_SERVICE_CLIENT_ID (default: permission-service)');
    console.log('   • PERMISSION_SERVICE_CLIENT_SECRET (required)');
    console.log('   • KEYCLOAK_BASE_URL (default: https://keycloak.ingka-dt.cn/auth)');
    console.log('   • PERMISSION_SERVICE_BASE (default: https://api-dev-mpp-fe.ingka-dt.cn)');
  }
  
  console.log('');
  console.log('⏰ **Important Notes:**');
  console.log('   • Tokens typically expire in 1 hour');
  console.log('   • This method doesn\'t require browser cookies');
  console.log('   • Uses service-to-service authentication');
  console.log('   • Suitable for automated/headless environments');
}

/**
 * Main test function
 */
async function runPermissionServiceAuthTest() {
  console.log('🔐 ==================== PERMISSION SERVICE AUTHENTICATION TEST ====================');
  console.log('🎯 Purpose: Test permission-service client credentials authentication\n');
  
  // Step 1: Check configuration
  const hasConfig = showConfigurationStatus();
  
  if (!hasConfig) {
    console.log('\n❌ Cannot proceed without proper configuration');
    console.log('   Please follow the configuration instructions above');
    return;
  }
  
  // Step 2: Test individual steps
  const stepResults = await testAuthenticationSteps();
  
  // Step 3: Test complete authentication
  const completeResult = await testCompleteAuthentication();
  
  // Step 4: Test session cookie integration
  const sessionCookie = await testSessionCookieIntegration();
  
  // Step 5: Show usage instructions
  showUsageInstructions(sessionCookie);
  
  console.log('\n🏁 ==================== TEST COMPLETE ====================');
  console.log('✅ Permission service authentication test finished');
  console.log('');
  console.log('🎯 **Final Results:**');
  console.log(`   Configuration: ${hasConfig ? 'Ready ✅' : 'Missing ❌'}`);
  console.log(`   Token Acquisition: ${stepResults?.tokenObtained ? 'Success ✅' : 'Failed ❌'}`);
  console.log(`   PM Token Creation: ${stepResults?.pmTokenCreated ? 'Success ✅' : 'Failed ❌'}`);
  console.log(`   API Integration: ${stepResults?.apiWorking ? 'Success ✅' : 'Failed ❌'}`);
  console.log(`   Complete Flow: ${completeResult ? 'Success ✅' : 'Failed ❌'}`);
  console.log(`   Session Cookie: ${sessionCookie ? 'Working ✅' : 'Failed ❌'}`);
  console.log('');
  
  if (sessionCookie) {
    console.log('🎉 **PERFECT! Permission service authentication is working!**');
    console.log('   Your MCP server can now authenticate without browser cookies!');
  } else {
    console.log('⚠️ **Needs attention - check configuration and credentials**');
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runPermissionServiceAuthTest().catch(error => {
    console.error('💥 Permission service authentication test failed:', error);
    process.exit(1);
  });
}

export { runPermissionServiceAuthTest };
