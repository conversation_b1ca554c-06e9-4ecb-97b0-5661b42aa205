#!/usr/bin/env node

/**
 * 🍪 Automated Cookie Extraction Test
 *
 * Purpose:
 * 1. Test the automated cookie extraction functionality
 * 2. Demonstrate how to obtain test_orders-portal cookie automatically
 * 3. Provide production-ready cookie for MCP server usage
 */

import { 
  extractCookieAutomatically, 
  getAutomatedSessionCookie,
  getProductionCookie,
  loadCookieExtractionConfig,
  extractKeycloakCookiesFromToken,
  testExtractedCookie
} from '../dist/auth/automated-cookie-extractor.js';

/**
 * Show configuration status
 */
function showConfigurationStatus() {
  console.log('📋 ==================== CONFIGURATION STATUS ====================');
  
  const config = loadCookieExtractionConfig();
  
  console.log('🔧 Configuration:');
  console.log(`   Permission Service: ${config.permissionServiceBase}`);
  console.log(`   Orders API: ${config.ordersApiBase}`);
  console.log(`   Client ID: ${config.clientId}`);
  console.log(`   Redirect URL: ${config.redirectUrl}`);
  console.log(`   Keycloak Base: ${config.keycloakBaseUrl}`);
  console.log(`   Keycloak Realm: ${config.keycloakRealm}`);
  console.log('');
  
  // Check Keycloak cookies
  const envCookies = config.keycloakCookies;
  const tokenCookies = extractKeycloakCookiesFromToken();
  
  console.log('🍪 Keycloak Cookies:');
  console.log(`   From Environment: ${envCookies ? 'Available ✅' : 'Not set ❌'}`);
  console.log(`   From Token File: ${tokenCookies ? 'Available ✅' : 'Not available ❌'}`);
  
  if (envCookies) {
    console.log(`   Environment cookies: ${envCookies.substring(0, 100)}...`);
  }
  
  if (tokenCookies) {
    console.log(`   Token-based cookies: ${tokenCookies.substring(0, 100)}...`);
  }
  
  console.log('');
  
  const hasRequiredConfig = (envCookies || tokenCookies);
  console.log(`🎯 Ready for extraction: ${hasRequiredConfig ? 'Yes ✅' : 'No ❌'}`);
  
  if (!hasRequiredConfig) {
    console.log('');
    console.log('⚠️ **Missing Configuration:**');
    console.log('   You need Keycloak session cookies to proceed.');
    console.log('   Either:');
    console.log('   1. Set KEYCLOAK_COOKIES environment variable with browser cookies');
    console.log('   2. Ensure mcp-mpc-odi.token.json contains valid session info');
    console.log('');
    console.log('📋 **How to get Keycloak cookies:**');
    console.log('   1. Go to https://admin.ingka-dt.cn/ in your browser');
    console.log('   2. Open Developer Tools (F12) → Network tab');
    console.log('   3. Look for requests to keycloak.ingka-dt.cn');
    console.log('   4. Copy Cookie header value');
    console.log('   5. Set: export KEYCLOAK_COOKIES="your-cookie-string"');
  }
  
  return hasRequiredConfig;
}

/**
 * Test automated cookie extraction
 */
async function testAutomatedExtraction() {
  console.log('\n🔄 ==================== AUTOMATED EXTRACTION TEST ====================');
  
  try {
    console.log('🚀 Starting automated cookie extraction...');
    console.log('   This will follow the complete OAuth2 flow automatically');
    console.log('');
    
    const result = await extractCookieAutomatically();
    
    console.log('📊 Extraction Results:');
    console.log(`   Success: ${result.success ? 'Yes ✅' : 'No ❌'}`);
    console.log(`   Method: ${result.method}`);
    
    if (result.success) {
      console.log(`   PM User Token: ${result.pmUserToken?.substring(0, 50)}...`);
      console.log(`   Cookie Value: ${result.cookieValue?.substring(0, 50)}...`);
      console.log(`   Expires At: ${result.expiresAt?.toISOString() || 'Unknown'}`);
      
      // Show decoded token info
      if (result.cookieValue) {
        try {
          const tokenString = Buffer.from(result.cookieValue, 'base64').toString();
          console.log(`   Decoded Token: ${tokenString}`);
          
          const parts = tokenString.split('@');
          if (parts.length === 2) {
            const ssoUUID = parts[0];
            const rightPart = parts[1];
            const rightParts = rightPart.split('.');
            
            if (rightParts.length >= 3) {
              const loginTime = rightParts[0];
              const userId = rightParts[1];
              const sessionTimeout = rightParts[rightParts.length - 1];
              
              console.log('   Token Details:');
              console.log(`     SSO UUID: ${ssoUUID}`);
              console.log(`     Login Time: ${new Date(parseInt(loginTime)).toISOString()}`);
              console.log(`     User ID: ${userId}`);
              console.log(`     Session Timeout: ${sessionTimeout}s`);
            }
          }
        } catch (decodeError) {
          console.log('   Could not decode token details');
        }
      }
      
      return result;
    } else {
      console.log(`   Error: ${result.error}`);
      return null;
    }
    
  } catch (error) {
    console.error('❌ Automated extraction test failed:', error);
    return null;
  }
}

/**
 * Test production cookie integration
 */
async function testProductionIntegration() {
  console.log('\n🏭 ==================== PRODUCTION INTEGRATION TEST ====================');
  
  try {
    console.log('🔄 Testing production cookie integration...');
    
    const result = await getProductionCookie();
    
    console.log('📊 Production Integration Results:');
    console.log(`   Success: ${result.success ? 'Yes ✅' : 'No ❌'}`);
    console.log(`   Method: ${result.method || 'N/A'}`);
    
    if (result.success && result.cookie) {
      console.log(`   Cookie: ${result.cookie.substring(0, 80)}...`);
      console.log(`   Expires At: ${result.expiresAt?.toISOString() || 'Unknown'}`);
      
      // Test the cookie with a real API call
      const cookieValue = result.cookie.split('=')[1];
      const apiTest = await testExtractedCookie(cookieValue);
      
      console.log(`   API Test: ${apiTest ? 'Passed ✅' : 'Failed ❌'}`);
      
      if (apiTest) {
        console.log('');
        console.log('🎉 **SUCCESS! Production cookie is working!**');
        console.log('   You can now use this cookie for MCP server API calls.');
        
        return result.cookie;
      }
    } else {
      console.log(`   Error: ${result.error}`);
    }
    
    return null;
    
  } catch (error) {
    console.error('❌ Production integration test failed:', error);
    return null;
  }
}

/**
 * Show usage instructions
 */
function showUsageInstructions(workingCookie) {
  console.log('\n🔧 ==================== USAGE INSTRUCTIONS ====================');
  
  if (workingCookie) {
    console.log('✅ **Your cookie is ready for production use!**');
    console.log('');
    console.log('📋 **For MCP Server Integration:**');
    console.log('```javascript');
    console.log('import { getProductionCookie } from "./auth/automated-cookie-extractor.js";');
    console.log('');
    console.log('// Get cookie automatically');
    console.log('const cookieResult = await getProductionCookie();');
    console.log('if (cookieResult.success) {');
    console.log('  const headers = {');
    console.log('    "Cookie": cookieResult.cookie,');
    console.log('    // other headers...');
    console.log('  };');
    console.log('}');
    console.log('```');
    console.log('');
    console.log('📋 **For Direct API Calls:**');
    console.log('```bash');
    const cookieValue = workingCookie.split('=')[1];
    console.log(`curl 'https://fe-dev-i.ingka-dt.cn/order-web/orders/search' \\`);
    console.log(`  -H 'content-type: application/json' \\`);
    console.log(`  -b '${workingCookie}' \\`);
    console.log(`  --data-raw '{"storeIds":[],"baseSize":10,"page":1,"size":10,"timestamp":${Date.now()}}'`);
    console.log('```');
    console.log('');
    console.log('📋 **Environment Variable (Alternative):**');
    console.log(`export STATIC_PM_USER_TOKEN="${cookieValue}"`);
    
  } else {
    console.log('❌ **Cookie extraction failed**');
    console.log('');
    console.log('🔧 **Troubleshooting Steps:**');
    console.log('   1. Ensure you have valid Keycloak session cookies');
    console.log('   2. Check that mcp-mpc-odi.token.json is up to date');
    console.log('   3. Verify network connectivity to permission service');
    console.log('   4. Try manual browser token extraction as fallback');
    console.log('');
    console.log('📋 **Manual Fallback:**');
    console.log('   Run: npm run test:extract-token');
    console.log('   Follow the browser extraction instructions');
  }
  
  console.log('');
  console.log('⏰ **Important Notes:**');
  console.log('   • Cookies typically expire in 1 hour');
  console.log('   • Implement automatic refresh in production');
  console.log('   • Monitor authentication success rates');
  console.log('   • Have fallback mechanisms ready');
}

/**
 * Main test function
 */
async function runAutomatedCookieTest() {
  console.log('🍪 ==================== AUTOMATED COOKIE EXTRACTION TEST ====================');
  console.log('🎯 Purpose: Test automated extraction of test_orders-portal cookie\n');
  
  // Step 1: Check configuration
  const hasConfig = showConfigurationStatus();
  
  if (!hasConfig) {
    console.log('\n❌ Cannot proceed without proper configuration');
    console.log('   Please follow the configuration instructions above');
    return;
  }
  
  // Step 2: Test automated extraction
  const extractionResult = await testAutomatedExtraction();
  
  // Step 3: Test production integration
  const productionCookie = await testProductionIntegration();
  
  // Step 4: Show usage instructions
  showUsageInstructions(productionCookie);
  
  console.log('\n🏁 ==================== TEST COMPLETE ====================');
  console.log('✅ Automated cookie extraction test finished');
  console.log('');
  console.log('🎯 **Final Results:**');
  console.log(`   Configuration: ${hasConfig ? 'Ready ✅' : 'Missing ❌'}`);
  console.log(`   Extraction: ${extractionResult ? 'Success ✅' : 'Failed ❌'}`);
  console.log(`   Production: ${productionCookie ? 'Working ✅' : 'Failed ❌'}`);
  console.log('');
  
  if (productionCookie) {
    console.log('🎉 **PERFECT! Automated cookie extraction is working!**');
    console.log('   Your MCP server can now authenticate automatically!');
  } else {
    console.log('⚠️ **Needs attention - check configuration and try again**');
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAutomatedCookieTest().catch(error => {
    console.error('💥 Automated cookie test failed:', error);
    process.exit(1);
  });
}

export { runAutomatedCookieTest };
