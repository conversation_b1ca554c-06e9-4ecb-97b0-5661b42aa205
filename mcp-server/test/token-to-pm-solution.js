#!/usr/bin/env node

/**
 * 🎯 Token Exchange to PM_User_Token Solution
 *
 * Purpose:
 * 1. Use token exchange (✅ Working)
 * 2. Use correct permission service endpoints
 * 3. Get working pm_user_token
 * 4. Test with orders API
 */

import axios from 'axios';
import { getEnvironmentConfig } from '../dist/utils/env.js';
import { decodeJWTUnsafe } from '../dist/utils/jwt-utils.js';
import fs from 'fs';
import path from 'path';

/**
 * Load configuration
 */
function loadConfiguration() {
  const env = getEnvironmentConfig();
  
  let tokenData = null;
  try {
    const tokenFile = path.join(process.cwd(), 'mcp-mpc-odi.token.json');
    const tokenContent = fs.readFileSync(tokenFile, 'utf8');
    tokenData = JSON.parse(tokenContent);
  } catch (error) {
    console.error('⚠️ Could not load token file:', error.message);
  }

  return {
    JWT_TOKEN: tokenData?.access_token || '',
    CLIENT_ID: env.OAUTH2_CLIENT_ID || 'mcp-mpc-odi',
    CLIENT_SECRET: env.OAUTH2_CLIENT_SECRET || '',
    KEYCLOAK_BASE_URL: env.KEYCLOAK_BASE_URL || 'https://keycloak.ingka-dt.cn/auth',
    KEYCLOAK_REALM: env.KEYCLOAK_REALM || 'master',
    
    // Correct endpoints from source code analysis
    PERMISSION_BACKEND: 'https://api-dev-mpp-fe.ingka-dt.cn/permission-service',
    PERMISSION_BFF: 'https://api-dev-mpp-fe.ingka-dt.cn/prm-auth',
    ORDERS_API: 'https://fe-dev-i.ingka-dt.cn/order-web'
  };
}

const CONFIG = loadConfiguration();

/**
 * Step 1: Exchange token (we know this works)
 */
async function exchangeToken() {
  console.log('🔄 ==================== STEP 1: TOKEN EXCHANGE ====================');
  
  try {
    const tokenEndpoint = `${CONFIG.KEYCLOAK_BASE_URL}/realms/${CONFIG.KEYCLOAK_REALM}/protocol/openid-connect/token`;
    const basicAuth = Buffer.from(`${CONFIG.CLIENT_ID}:${CONFIG.CLIENT_SECRET}`).toString('base64');
    
    const params = new URLSearchParams({
      grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
      subject_token: CONFIG.JWT_TOKEN,
      subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
      requested_token_type: 'urn:ietf:params:oauth:token-type:access_token'
    });

    const response = await axios.post(tokenEndpoint, params, {
      headers: {
        'Authorization': `Basic ${basicAuth}`,
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      timeout: 10000
    });

    if (response.data.access_token) {
      console.log('✅ Token exchange successful!');
      const payload = decodeJWTUnsafe(response.data.access_token);
      console.log(`   User: ${payload.email} (${payload.name})`);
      console.log(`   Expires in: ${response.data.expires_in}s`);
      
      return {
        success: true,
        accessToken: response.data.access_token,
        sessionId: payload.sid,
        userId: payload.sub,
        email: payload.email
      };
    }
    
    throw new Error('No access token in response');
    
  } catch (error) {
    console.error('❌ Token exchange failed:', error.message);
    return { success: false };
  }
}

/**
 * Step 2: Use exchanged token with correct permission service backend
 */
async function testPermissionServiceBackend(exchangedToken) {
  console.log('\n🔄 ==================== STEP 2: PERMISSION SERVICE BACKEND ====================');
  
  try {
    console.log('🔄 Testing exchanged token with permission service backend...');
    
    // Use the correct endpoint from source code: /rs/user/info
    const userInfoUrl = `${CONFIG.PERMISSION_BACKEND}/rs/user/info`;
    
    console.log(`   URL: ${userInfoUrl}`);
    console.log(`   Method: Bearer authentication`);
    
    const response = await axios.get(userInfoUrl, {
      headers: {
        'Authorization': `Bearer ${exchangedToken}`,
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200 && response.data?.success) {
      console.log('✅ Permission service backend accepts exchanged token!');
      return { success: true, userInfo: response.data.data };
    } else {
      console.log('❌ Permission service backend rejected token');
      return { success: false, error: response.data };
    }
    
  } catch (error) {
    console.error('❌ Permission service backend test failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Step 3: Try to get login URL and complete OAuth2 flow with exchanged token
 */
async function tryCompleteOAuth2Flow(exchangedToken, sessionInfo) {
  console.log('\n🔄 ==================== STEP 3: COMPLETE OAUTH2 FLOW ====================');
  
  try {
    console.log('🔄 Attempting to complete OAuth2 flow with exchanged token...');
    
    // Step 3a: Get login URL from BFF
    const getLoginUrl = `${CONFIG.PERMISSION_BFF}/auth/getLoginUrl`;
    
    console.log(`\n📋 Step 3a: Get login URL`);
    console.log(`   URL: ${getLoginUrl}`);
    
    const loginUrlResponse = await axios.get(getLoginUrl, {
      headers: {
        'clientId': 'orders-portal',
        'Accept': 'application/json'
      },
      params: {
        redirectUrl: 'https://fe-dev-i.ingka-dt.cn/order-web/user/current',
        referer: 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${loginUrlResponse.status}`);
    console.log(`   Response data: ${JSON.stringify(loginUrlResponse.data, null, 2)}`);
    
    if (loginUrlResponse.data?.success && loginUrlResponse.data?.data) {
      const loginUrl = loginUrlResponse.data.data;
      console.log('✅ Got login URL from BFF');
      console.log(`   Login URL: ${loginUrl}`);
      
      // Extract state from login URL
      const urlParams = new URLSearchParams(loginUrl.split('?')[1]);
      const state = urlParams.get('state');
      
      // Step 3b: Try to simulate loginComplete with our session
      console.log(`\n📋 Step 3b: Simulate loginComplete`);
      
      const loginCompleteUrl = `${CONFIG.PERMISSION_BFF}/auth/loginComplete`;
      const fakeCode = `${sessionInfo.sessionId}.${sessionInfo.userId}.${Date.now()}`;
      
      console.log(`   URL: ${loginCompleteUrl}`);
      console.log(`   State: ${state}`);
      console.log(`   Code: ${fakeCode.substring(0, 50)}...`);
      
      const loginCompleteResponse = await axios.get(loginCompleteUrl, {
        params: {
          code: fakeCode,
          state: state,
          session_state: sessionInfo.sessionId
        },
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        },
        maxRedirects: 0,
        timeout: 10000,
        validateStatus: () => true
      });
      
      console.log(`   Response status: ${loginCompleteResponse.status}`);
      
      if (loginCompleteResponse.status === 302 && loginCompleteResponse.headers.location) {
        const redirectUrl = loginCompleteResponse.headers.location;
        console.log('✅ Got redirect from loginComplete!');
        console.log(`   Redirect URL: ${redirectUrl}`);
        
        // Check for pm_user_token in redirect
        if (redirectUrl.includes('pm_user_token=')) {
          const urlParams = new URLSearchParams(redirectUrl.split('?')[1]);
          const pmUserToken = urlParams.get('pm_user_token');
          
          if (pmUserToken) {
            console.log('🎉 SUCCESS! Got pm_user_token from OAuth2 flow!');
            console.log(`   pm_user_token: ${pmUserToken}`);
            
            return {
              success: true,
              pmUserToken,
              method: 'OAuth2 Flow'
            };
          }
        }
      }
      
      console.log('❌ OAuth2 flow simulation failed');
      if (loginCompleteResponse.data) {
        console.log(`   Response: ${JSON.stringify(loginCompleteResponse.data, null, 2)}`);
      }
      
      return { success: false };
      
    } else {
      console.log('❌ Failed to get login URL from BFF');
      return { success: false };
    }
    
  } catch (error) {
    console.error('❌ OAuth2 flow simulation failed:', error.message);
    return { success: false };
  }
}

/**
 * Step 4: Generate pm_user_token based on session info
 */
function generatePmUserToken(sessionInfo) {
  console.log('\n🔄 ==================== STEP 4: GENERATE PM_USER_TOKEN ====================');
  
  try {
    console.log('🔄 Generating pm_user_token from session info...');
    
    const currentTime = Date.now();
    const sessionTimeout = 3600; // 1 hour
    const ssoUUID = sessionInfo.sessionId;
    
    // Format: <EMAIL>
    const tokenContent = `${ssoUUID}@${currentTime}.${sessionInfo.userId}.${sessionTimeout}`;
    const pmUserToken = Buffer.from(tokenContent).toString('base64');
    
    console.log(`   Token content: ${tokenContent}`);
    console.log(`   pm_user_token: ${pmUserToken}`);
    
    return {
      success: true,
      pmUserToken,
      tokenContent,
      method: 'Generated'
    };
    
  } catch (error) {
    console.error('❌ pm_user_token generation failed:', error.message);
    return { success: false };
  }
}

/**
 * Step 5: Test pm_user_token with orders API
 */
async function testWithOrdersAPI(pmUserToken) {
  console.log('\n🧪 ==================== STEP 5: TEST WITH ORDERS API ====================');
  
  if (!pmUserToken) {
    console.log('❌ No pm_user_token to test');
    return false;
  }
  
  try {
    console.log('🔄 Testing pm_user_token with orders API...');
    console.log(`   Token: ${pmUserToken.substring(0, 50)}...`);
    
    const testUrl = `${CONFIG.ORDERS_API}/orders/search`;
    const requestData = {
      "storeIds": [],
      "baseSize": 10,
      "page": 1,
      "size": 10,
      "timestamp": Date.now()
    };
    
    const response = await axios.post(testUrl, requestData, {
      headers: {
        'accept': 'application/json, text/plain, */*',
        'content-type': 'application/json',
        'Cookie': `test_orders-portal=${pmUserToken}`,
        'origin': 'https://admin.ingka-dt.cn',
        'referer': 'https://admin.ingka-dt.cn/',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'x-custom-referrer': 'https://admin.ingka-dt.cn/app/orders-portal/oms/index'
      },
      timeout: 10000,
      validateStatus: () => true
    });
    
    console.log(`   Response status: ${response.status}`);
    console.log(`   Response data: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200 && response.data && response.data.code !== 401) {
      console.log('🎉 SUCCESS! pm_user_token works with orders API!');
      return true;
    } else {
      console.log('❌ pm_user_token failed with orders API');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Orders API test failed:', error.message);
    return false;
  }
}

/**
 * Main function
 */
async function tokenToPmSolution() {
  console.log('🎯 ==================== TOKEN EXCHANGE TO PM_USER_TOKEN SOLUTION ====================');
  console.log('🎯 Purpose: Complete working solution from token exchange to orders API\n');
  
  console.log('📋 Configuration:');
  console.log(`   JWT Token: ${CONFIG.JWT_TOKEN ? 'Available ✅' : 'Missing ❌'}`);
  console.log(`   Client Secret: ${CONFIG.CLIENT_SECRET ? 'Available ✅' : 'Missing ❌'}`);
  console.log(`   Permission Backend: ${CONFIG.PERMISSION_BACKEND}`);
  console.log(`   Permission BFF: ${CONFIG.PERMISSION_BFF}`);
  console.log(`   Orders API: ${CONFIG.ORDERS_API}`);
  console.log('');
  
  if (!CONFIG.JWT_TOKEN || !CONFIG.CLIENT_SECRET) {
    console.log('❌ Missing required configuration');
    return;
  }
  
  const results = {};
  
  // Step 1: Exchange token
  const exchangeResult = await exchangeToken();
  results.tokenExchange = exchangeResult.success;
  
  if (!exchangeResult.success) {
    console.log('❌ Cannot proceed without token exchange');
    return;
  }
  
  // Step 2: Test permission service backend
  const backendResult = await testPermissionServiceBackend(exchangeResult.accessToken);
  results.permissionBackend = backendResult.success;
  
  // Step 3: Try complete OAuth2 flow
  const oauthResult = await tryCompleteOAuth2Flow(exchangeResult.accessToken, exchangeResult);
  results.oauthFlow = oauthResult.success;
  
  // Step 4: Generate pm_user_token
  const generateResult = generatePmUserToken(exchangeResult);
  results.tokenGeneration = generateResult.success;
  
  // Step 5: Test with orders API
  const pmUserToken = oauthResult.pmUserToken || generateResult.pmUserToken;
  const apiResult = await testWithOrdersAPI(pmUserToken);
  results.ordersAPI = apiResult;
  
  console.log('\n🏁 ==================== SOLUTION RESULTS ====================');
  console.log('✅ Token exchange to pm_user_token solution finished');
  console.log('');
  console.log('🎯 **Final Results:**');
  console.log(`   Token Exchange: ${results.tokenExchange ? 'SUCCESS ✅' : 'Failed ❌'}`);
  console.log(`   Permission Backend: ${results.permissionBackend ? 'SUCCESS ✅' : 'Failed ❌'}`);
  console.log(`   OAuth2 Flow: ${results.oauthFlow ? 'SUCCESS ✅' : 'Failed ❌'}`);
  console.log(`   Token Generation: ${results.tokenGeneration ? 'SUCCESS ✅' : 'Failed ❌'}`);
  console.log(`   Orders API: ${results.ordersAPI ? 'SUCCESS ✅' : 'Failed ❌'}`);
  console.log('');
  
  if (results.tokenExchange && results.ordersAPI) {
    console.log('🎉 **COMPLETE SUCCESS!**');
    console.log('   End-to-end solution working from token exchange to orders API!');
    console.log('');
    console.log('🔧 **Integration Code:**');
    console.log('```javascript');
    console.log('// 1. Exchange token');
    console.log('const exchangedToken = await exchangeToken();');
    console.log('');
    console.log('// 2. Generate pm_user_token');
    console.log('const pmUserToken = generatePmUserToken(exchangedToken);');
    console.log('');
    console.log('// 3. Use with orders API');
    console.log('headers: {');
    console.log(`  'Cookie': 'test_orders-portal=\${pmUserToken}',`);
    console.log('  // other headers...');
    console.log('}');
    console.log('```');
  } else if (results.tokenExchange) {
    console.log('🎯 **Partial Success**');
    console.log('   Token exchange works, but pm_user_token needs refinement');
    console.log('   Consider browser token extraction as fallback');
  } else {
    console.log('❌ **Needs Configuration**');
    console.log('   Check Keycloak token exchange configuration');
  }
}

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  tokenToPmSolution().catch(error => {
    console.error('💥 Solution failed:', error);
    process.exit(1);
  });
}

export { tokenToPmSolution };
