#!/usr/bin/env node

/**
 * 🔐 Per-Request Authentication Test
 *
 * Purpose:
 * 1. Test MCP specification compliance for per-request token validation
 * 2. Verify token caching performance optimization
 * 3. Test authentication on every HTTP request to /mcp endpoint
 */

import { 
  validateRequestToken,
  loadPerRequestAuthConfig,
  getTokenCacheStats,
  clearTokenCache
} from '../dist/auth/per-request-auth.js';

/**
 * Mock request object for testing
 */
function createMockRequest(token) {
  return {
    headers: {
      authorization: token ? `Bearer ${token}` : undefined,
      'user-agent': 'MCP-PerRequest-Test/1.0.0'
    },
    method: 'POST',
    path: '/mcp',
    body: { method: 'tools/list' }
  };
}

/**
 * Mock OAuth2 provider
 */
function createMockProvider() {
  return {
    verifyAccessToken: async (token) => {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 50));
      
      if (token === 'valid-token') {
        return {
          token,
          clientId: 'permission-service',
          scopes: ['profile', 'roles', 'email'],
          expiresAt: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
          extra: {
            userInfo: {
              sub: 'test-user',
              azp: 'permission-service',
              exp: Math.floor(Date.now() / 1000) + 3600
            }
          }
        };
      }
      throw new Error('Invalid token');
    }
  };
}

/**
 * Test per-request authentication configuration
 */
function testPerRequestAuthConfig() {
  console.log('📋 ==================== PER-REQUEST AUTH CONFIGURATION ====================');
  
  const config = loadPerRequestAuthConfig();
  
  console.log('🔧 Configuration:');
  console.log(`   Enabled: ${config.enabled ? 'Yes ✅' : 'No ❌'}`);
  console.log(`   Cache Enabled: ${config.cacheEnabled ? 'Yes ✅' : 'No ❌'}`);
  console.log(`   Cache Max Age: ${config.cacheMaxAge} seconds`);
  console.log(`   Log Validation: ${config.logValidation ? 'Yes ✅' : 'No ❌'}`);
  console.log(`   Skip Paths: [${config.skipPaths?.join(', ') || 'none'}]`);
  console.log('');
  
  if (!config.enabled) {
    console.log('⚠️ **Per-request authentication is disabled**');
    console.log('   Set PER_REQUEST_AUTH_ENABLED=true to enable MCP compliance');
    console.log('');
  }
  
  return config;
}

/**
 * Test token validation performance
 */
async function testTokenValidationPerformance() {
  console.log('⚡ ==================== TOKEN VALIDATION PERFORMANCE ====================');
  
  const provider = createMockProvider();
  const request = createMockRequest('valid-token');
  
  // Clear cache to start fresh
  clearTokenCache();
  
  console.log('🔄 Testing validation performance...');
  
  // First validation (no cache)
  console.log('1️⃣ First validation (no cache):');
  const result1 = await validateRequestToken(request, provider);
  console.log(`   Success: ${result1.success ? 'Yes ✅' : 'No ❌'}`);
  console.log(`   Cached: ${result1.cached ? 'Yes' : 'No'}`);
  console.log(`   Duration: ${result1.duration}ms`);
  
  // Second validation (should be cached)
  console.log('2️⃣ Second validation (should be cached):');
  const result2 = await validateRequestToken(request, provider);
  console.log(`   Success: ${result2.success ? 'Yes ✅' : 'No ❌'}`);
  console.log(`   Cached: ${result2.cached ? 'Yes ✅' : 'No ❌'}`);
  console.log(`   Duration: ${result2.duration}ms`);
  
  // Performance comparison
  const speedup = result1.duration / result2.duration;
  console.log('📊 Performance Analysis:');
  console.log(`   Cache speedup: ${speedup.toFixed(1)}x faster`);
  console.log(`   Time saved: ${result1.duration - result2.duration}ms`);
  
  // Cache statistics
  const cacheStats = getTokenCacheStats();
  console.log('📈 Cache Statistics:');
  console.log(`   Total entries: ${cacheStats.totalEntries}`);
  console.log(`   Valid entries: ${cacheStats.validEntries}`);
  console.log(`   Expired entries: ${cacheStats.expiredEntries}`);
  
  console.log('');
  
  return {
    firstValidation: result1,
    secondValidation: result2,
    speedup,
    cacheStats
  };
}

/**
 * Test multiple concurrent requests
 */
async function testConcurrentRequests() {
  console.log('🚀 ==================== CONCURRENT REQUESTS TEST ====================');
  
  const provider = createMockProvider();
  clearTokenCache();
  
  console.log('🔄 Testing 10 concurrent requests...');
  
  const requests = Array.from({ length: 10 }, (_, i) => 
    validateRequestToken(createMockRequest('valid-token'), provider)
  );
  
  const startTime = Date.now();
  const results = await Promise.all(requests);
  const totalTime = Date.now() - startTime;
  
  const successCount = results.filter(r => r.success).length;
  const cachedCount = results.filter(r => r.cached).length;
  const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
  
  console.log('📊 Concurrent Request Results:');
  console.log(`   Total requests: ${requests.length}`);
  console.log(`   Successful: ${successCount}/${requests.length} ✅`);
  console.log(`   Cached responses: ${cachedCount}/${requests.length}`);
  console.log(`   Total time: ${totalTime}ms`);
  console.log(`   Average duration: ${avgDuration.toFixed(1)}ms`);
  console.log(`   Requests per second: ${(requests.length / totalTime * 1000).toFixed(1)}`);
  
  console.log('');
  
  return {
    totalRequests: requests.length,
    successCount,
    cachedCount,
    totalTime,
    avgDuration
  };
}

/**
 * Test error scenarios
 */
async function testErrorScenarios() {
  console.log('❌ ==================== ERROR SCENARIOS TEST ====================');
  
  const provider = createMockProvider();
  
  const testCases = [
    {
      name: 'Missing Authorization Header',
      request: createMockRequest(null),
      expectedError: 'Missing or invalid Authorization header'
    },
    {
      name: 'Invalid Token Format',
      request: createMockRequest('invalid-format'),
      expectedError: 'Invalid token'
    },
    {
      name: 'No OAuth2 Provider',
      request: createMockRequest('valid-token'),
      provider: undefined,
      expectedError: 'OAuth2 provider not available'
    }
  ];
  
  const results = [];
  
  for (const testCase of testCases) {
    console.log(`🔄 Testing: ${testCase.name}`);
    
    try {
      const result = await validateRequestToken(testCase.request, testCase.provider || provider);
      
      console.log(`   Success: ${result.success ? 'Yes ⚠️' : 'No ✅'}`);
      console.log(`   Error: ${result.error || 'None'}`);
      console.log(`   Duration: ${result.duration}ms`);
      
      const passed = !result.success && result.error?.includes(testCase.expectedError.split(' ')[0]);
      console.log(`   Test Result: ${passed ? 'PASS ✅' : 'FAIL ❌'}`);
      
      results.push({
        name: testCase.name,
        success: result.success,
        error: result.error,
        passed
      });
      
    } catch (error) {
      console.log(`   Exception: ${error.message} ❌`);
      results.push({
        name: testCase.name,
        success: false,
        error: error.message,
        passed: false
      });
    }
    
    console.log('');
  }
  
  return results;
}

/**
 * Provide MCP compliance recommendations
 */
function provideMCPComplianceRecommendations(config, performanceResults, errorResults) {
  console.log('💡 ==================== MCP COMPLIANCE RECOMMENDATIONS ====================');
  
  console.log('📋 **MCP Specification Requirements:**');
  console.log('   "authorization MUST be included in every HTTP request from client to server,');
  console.log('   even if they are part of the same logical session."');
  console.log('');
  
  if (config.enabled) {
    console.log('✅ **Per-request authentication is enabled**');
    console.log('   Your server is MCP specification compliant!');
    console.log('');
    
    console.log('🔧 **Performance Optimizations:**');
    if (config.cacheEnabled) {
      console.log(`   ✅ Token caching enabled (${config.cacheMaxAge}s max age)`);
      console.log(`   ✅ Cache speedup: ${performanceResults.speedup.toFixed(1)}x faster`);
    } else {
      console.log('   ⚠️ Token caching disabled - consider enabling for better performance');
    }
    console.log('');
    
    console.log('📊 **Performance Metrics:**');
    console.log(`   Average validation time: ${performanceResults.firstValidation.duration}ms (uncached)`);
    console.log(`   Average validation time: ${performanceResults.secondValidation.duration}ms (cached)`);
    console.log(`   Concurrent request handling: ${(10 / performanceResults.totalTime * 1000).toFixed(1)} req/s`);
    
  } else {
    console.log('❌ **Per-request authentication is disabled**');
    console.log('   Your server is NOT MCP specification compliant');
    console.log('');
    console.log('🔧 **To enable MCP compliance:**');
    console.log('   Set PER_REQUEST_AUTH_ENABLED=true in your .env file');
    console.log('   This will validate Bearer tokens on every /mcp request');
  }
  
  console.log('');
  console.log('🔒 **Security Benefits:**');
  console.log('   • Validates tokens haven\'t been revoked between requests');
  console.log('   • Handles token expiration properly');
  console.log('   • Prevents session hijacking attacks');
  console.log('   • Ensures fresh authorization for each operation');
  
  const passedErrorTests = errorResults.filter(r => r.passed).length;
  console.log('');
  console.log(`🧪 **Error Handling: ${passedErrorTests}/${errorResults.length} tests passed**`);
  
  if (passedErrorTests === errorResults.length) {
    console.log('   ✅ All error scenarios handled correctly');
  } else {
    console.log('   ⚠️ Some error scenarios need attention');
  }
}

/**
 * Main test function
 */
async function runPerRequestAuthTest() {
  console.log('🔐 ==================== PER-REQUEST AUTHENTICATION TEST ====================');
  console.log('🎯 Purpose: Test MCP specification compliance for per-request token validation\n');
  
  // Step 1: Test configuration
  const config = testPerRequestAuthConfig();
  
  // Step 2: Test performance
  const performanceResults = await testTokenValidationPerformance();
  
  // Step 3: Test concurrent requests
  const concurrentResults = await testConcurrentRequests();
  
  // Step 4: Test error scenarios
  const errorResults = await testErrorScenarios();
  
  // Step 5: Provide recommendations
  provideMCPComplianceRecommendations(config, performanceResults, errorResults);
  
  console.log('\n🏁 ==================== TEST COMPLETE ====================');
  console.log('✅ Per-request authentication test finished');
  console.log('');
  console.log('🎯 **Final Results:**');
  console.log(`   Configuration: ${config.enabled ? 'Enabled ✅' : 'Disabled ❌'}`);
  console.log(`   Performance: ${performanceResults.speedup.toFixed(1)}x cache speedup ⚡`);
  console.log(`   Concurrency: ${(10 / concurrentResults.totalTime * 1000).toFixed(1)} req/s 🚀`);
  console.log(`   Error Handling: ${errorResults.filter(r => r.passed).length}/${errorResults.length} passed 🧪`);
  console.log('');
  
  if (config.enabled) {
    console.log('🎉 **MCP Specification Compliance: ACHIEVED!**');
    console.log('   Your server validates Bearer tokens on every request as required');
  } else {
    console.log('⚠️ **MCP Specification Compliance: NOT ACHIEVED**');
    console.log('   Enable per-request authentication for full compliance');
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runPerRequestAuthTest().catch(error => {
    console.error('💥 Per-request authentication test failed:', error);
    process.exit(1);
  });
}

export { runPerRequestAuthTest };
