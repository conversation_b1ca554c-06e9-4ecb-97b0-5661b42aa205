#!/usr/bin/env node

/**
 * 🎯 Production Authentication Test
 *
 * Purpose:
 * 1. Test the complete production authentication solution
 * 2. Show all authentication methods
 * 3. Provide clear integration instructions
 */

import { 
  authenticate, 
  getAuthStatus, 
  getSessionCookie,
  testPmUserToken,
  getStaticPmUserToken
} from '../dist/auth/production-auth-solution.js';

/**
 * Test static token method
 */
async function testStaticTokenMethod() {
  console.log('🔄 ==================== METHOD 1: STATIC TOKEN ====================');
  
  const staticToken = getStaticPmUserToken();
  
  if (!staticToken) {
    console.log('❌ No static pm_user_token configured');
    console.log('   To configure: echo "STATIC_PM_USER_TOKEN=your-token" >> .env');
    return { success: false, method: 'Static Token' };
  }
  
  console.log('🔄 Testing static pm_user_token...');
  console.log(`   Token: ${staticToken.substring(0, 50)}...`);
  
  const isWorking = await testPmUserToken(staticToken);
  
  if (isWorking) {
    console.log('✅ Static pm_user_token works!');
    console.log('   This is the most reliable method');
    return { success: true, method: 'Static Token', pmUserToken: staticToken };
  } else {
    console.log('❌ Static pm_user_token expired or invalid');
    console.log('   Extract a fresh token from browser');
    return { success: false, method: 'Static Token' };
  }
}

/**
 * Test token exchange method
 */
async function testTokenExchangeMethod() {
  console.log('\n🔄 ==================== METHOD 2: TOKEN EXCHANGE ====================');
  
  console.log('🔄 Testing token exchange method...');
  
  const result = await authenticate();
  
  if (result.success && result.method?.includes('Token Exchange')) {
    console.log('✅ Token exchange method works!');
    console.log(`   Method: ${result.method}`);
    console.log(`   Token: ${result.pmUserToken?.substring(0, 50)}...`);
    if (result.expiresAt) {
      console.log(`   Expires: ${result.expiresAt.toISOString()}`);
    }
    return { success: true, method: 'Token Exchange', pmUserToken: result.pmUserToken };
  } else {
    console.log('❌ Token exchange method failed');
    console.log(`   Error: ${result.error || 'Unknown error'}`);
    return { success: false, method: 'Token Exchange' };
  }
}

/**
 * Test session cookie generation
 */
async function testSessionCookieGeneration() {
  console.log('\n🔄 ==================== SESSION COOKIE GENERATION ====================');
  
  console.log('🔄 Testing session cookie generation...');
  
  const sessionCookie = await getSessionCookie();
  
  if (sessionCookie) {
    console.log('✅ Session cookie generated successfully!');
    console.log(`   Cookie: ${sessionCookie.substring(0, 70)}...`);
    return { success: true, sessionCookie };
  } else {
    console.log('❌ Session cookie generation failed');
    return { success: false };
  }
}

/**
 * Show integration instructions
 */
function showIntegrationInstructions(results) {
  console.log('\n🔧 ==================== INTEGRATION INSTRUCTIONS ====================');
  
  const workingMethod = results.find(r => r.success);
  
  if (workingMethod) {
    console.log('🎉 **SUCCESS! Here\'s how to integrate with your MCP server:**');
    console.log('');
    console.log('**Method 1: Direct Integration**');
    console.log('```typescript');
    console.log('import { getSessionCookie } from "./auth/production-auth-solution.js";');
    console.log('');
    console.log('// In your service adapter');
    console.log('const sessionCookie = await getSessionCookie();');
    console.log('if (sessionCookie) {');
    console.log('  // Use in API requests');
    console.log('  headers: {');
    console.log('    "Cookie": sessionCookie,');
    console.log('    // other headers...');
    console.log('  }');
    console.log('}');
    console.log('```');
    console.log('');
    console.log('**Method 2: Environment Variable (Recommended)**');
    console.log('```bash');
    console.log('# Add to .env file');
    console.log(`STATIC_PM_USER_TOKEN=${workingMethod.pmUserToken || 'your-browser-token'}`);
    console.log('```');
    console.log('');
    console.log('**Method 3: Automatic Refresh**');
    console.log('```typescript');
    console.log('import { refreshAuthIfNeeded } from "./auth/production-auth-solution.js";');
    console.log('');
    console.log('// Refresh authentication periodically');
    console.log('setInterval(async () => {');
    console.log('  await refreshAuthIfNeeded();');
    console.log('}, 30 * 60 * 1000); // Every 30 minutes');
    console.log('```');
  } else {
    console.log('❌ **No working authentication method found**');
    console.log('');
    console.log('**Next Steps:**');
    console.log('1. **Extract pm_user_token from browser:**');
    console.log('   • Go to https://admin.ingka-dt.cn/app/orders-portal/oms/index');
    console.log('   • Open Developer Tools → Network tab');
    console.log('   • Look for requests to fe-dev-i.ingka-dt.cn/order-web/');
    console.log('   • Find Cookie header with test_orders-portal=...');
    console.log('   • Copy the Base64 token value');
    console.log('');
    console.log('2. **Configure static token:**');
    console.log('   ```bash');
    console.log('   echo "STATIC_PM_USER_TOKEN=your-browser-token" >> .env');
    console.log('   ```');
    console.log('');
    console.log('3. **Test again:**');
    console.log('   ```bash');
    console.log('   npm run test:production-auth');
    console.log('   ```');
  }
  
  console.log('');
  console.log('**Production Recommendations:**');
  console.log('• **Monitor token expiration** and refresh automatically');
  console.log('• **Implement fallback mechanisms** for token failures');
  console.log('• **Log authentication events** for debugging');
  console.log('• **Cache working tokens** to avoid unnecessary requests');
  console.log('• **Handle authentication errors gracefully** in your MCP tools');
}

/**
 * Main test function
 */
async function testProductionAuth() {
  console.log('🎯 ==================== PRODUCTION AUTHENTICATION TEST ====================');
  console.log('🎯 Purpose: Test complete production authentication solution\n');
  
  const results = [];
  
  // Test Method 1: Static Token
  const staticResult = await testStaticTokenMethod();
  results.push(staticResult);
  
  // Test Method 2: Token Exchange (only if static failed)
  if (!staticResult.success) {
    const exchangeResult = await testTokenExchangeMethod();
    results.push(exchangeResult);
  }
  
  // Test Session Cookie Generation
  const cookieResult = await testSessionCookieGeneration();
  results.push(cookieResult);
  
  // Show integration instructions
  showIntegrationInstructions(results);
  
  console.log('\n🏁 ==================== PRODUCTION AUTH TEST COMPLETE ====================');
  console.log('✅ Production authentication test finished');
  console.log('');
  console.log('🎯 **Final Results:**');
  results.forEach((result, index) => {
    console.log(`   Method ${index + 1} (${result.method}): ${result.success ? 'SUCCESS ✅' : 'Failed ❌'}`);
  });
  console.log('');
  
  const workingMethods = results.filter(r => r.success);
  if (workingMethods.length > 0) {
    console.log('🎉 **EXCELLENT! You have working authentication!**');
    console.log(`   Working methods: ${workingMethods.length}/${results.length}`);
    console.log('   Your MCP server is ready for production use!');
  } else {
    console.log('⚠️ **Authentication needs setup**');
    console.log('   Follow the integration instructions above');
    console.log('   Extract pm_user_token from browser for immediate use');
  }
  
  console.log('');
  console.log('🚀 **Next Steps:**');
  console.log('   1. Integrate working authentication with your MCP server');
  console.log('   2. Test with MCP Inspector');
  console.log('   3. Implement automatic token refresh');
  console.log('   4. Monitor authentication in production');
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testProductionAuth().catch(error => {
    console.error('💥 Production authentication test failed:', error);
    process.exit(1);
  });
}

export { testProductionAuth };
