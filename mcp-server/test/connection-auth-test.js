#!/usr/bin/env node

/**
 * 🔐 Connection Authentication Test
 *
 * Purpose:
 * 1. Test connection-time authentication validation
 * 2. Verify OAuth2 and cookie authentication during MCP connection
 * 3. Test authentication configuration options
 */

import { 
  validateConnection,
  validateOAuth2Connection,
  validateCookieConnection,
  testApiConnection,
  loadConnectionAuthConfig
} from '../dist/auth/connection-auth.js';

/**
 * Mock request object for testing
 */
function createMockRequest(options = {}) {
  return {
    headers: {
      authorization: options.authorization,
      cookie: options.cookie,
      'user-agent': 'MCP-Connection-Test/1.0.0',
      ...options.headers
    },
    method: 'POST',
    path: '/mcp',
    body: options.body || {}
  };
}

/**
 * Test connection authentication configuration
 */
function testConnectionAuthConfig() {
  console.log('📋 ==================== CONNECTION AUTH CONFIGURATION ====================');
  
  const config = loadConnectionAuthConfig();
  
  console.log('🔧 Configuration:');
  console.log(`   Enabled: ${config.enabled ? 'Yes ✅' : 'No ❌'}`);
  console.log(`   Strict Mode: ${config.strict ? 'Yes ✅' : 'No ❌'}`);
  console.log(`   API Testing: ${config.testApi ? 'Yes ✅' : 'No ❌'}`);
  console.log(`   Required Scopes: ${config.requiredScopes?.join(', ') || 'None'}`);
  console.log(`   Allowed Clients: ${config.allowedClients?.join(', ') || 'Any'}`);
  console.log('');
  
  if (!config.enabled) {
    console.log('⚠️ **Connection authentication is disabled**');
    console.log('   Set CONNECTION_AUTH_ENABLED=true to enable');
    console.log('');
  }
  
  return config;
}

/**
 * Test cookie authentication validation
 */
async function testCookieAuthentication() {
  console.log('🍪 ==================== COOKIE AUTHENTICATION TEST ====================');
  
  try {
    // Test with valid cookie
    console.log('🔄 Testing with valid cookie...');
    const validCookieReq = createMockRequest({
      cookie: 'test_orders-portal=OTJmNjk3MGItZGY5My00MzM3LWE4YjItMTZiZTczYjViZTc5QDE3NTM0MjYwOTAzNjIuM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA='
    });
    
    const validResult = await validateCookieConnection(validCookieReq);
    console.log('📊 Valid Cookie Result:');
    console.log(`   Success: ${validResult.success ? 'Yes ✅' : 'No ❌'}`);
    if (validResult.success) {
      console.log(`   Client ID: ${validResult.clientId}`);
      console.log(`   User ID: ${validResult.userInfo?.userId}`);
      console.log(`   Expires At: ${validResult.userInfo?.expiresAt?.toISOString()}`);
    } else {
      console.log(`   Error: ${validResult.error}`);
    }
    console.log('');
    
    // Test with missing cookie
    console.log('🔄 Testing with missing cookie...');
    const noCookieReq = createMockRequest({});
    
    const noCookieResult = await validateCookieConnection(noCookieReq);
    console.log('📊 Missing Cookie Result:');
    console.log(`   Success: ${noCookieResult.success ? 'Yes ✅' : 'No ❌'}`);
    console.log(`   Error: ${noCookieResult.error}`);
    console.log('');
    
    // Test with invalid cookie
    console.log('🔄 Testing with invalid cookie...');
    const invalidCookieReq = createMockRequest({
      cookie: 'test_orders-portal=invalid-token'
    });
    
    const invalidResult = await validateCookieConnection(invalidCookieReq);
    console.log('📊 Invalid Cookie Result:');
    console.log(`   Success: ${invalidResult.success ? 'Yes ✅' : 'No ❌'}`);
    console.log(`   Error: ${invalidResult.error}`);
    console.log('');
    
    return {
      validCookie: validResult.success,
      noCookie: !noCookieResult.success,
      invalidCookie: !invalidResult.success
    };
    
  } catch (error) {
    console.error('❌ Cookie authentication test failed:', error);
    return {
      validCookie: false,
      noCookie: false,
      invalidCookie: false
    };
  }
}

/**
 * Test OAuth2 authentication validation
 */
async function testOAuth2Authentication() {
  console.log('🔐 ==================== OAUTH2 AUTHENTICATION TEST ====================');
  
  try {
    // Test with missing OAuth2 provider
    console.log('🔄 Testing with missing OAuth2 provider...');
    const noProviderReq = createMockRequest({
      authorization: 'Bearer test-token'
    });
    
    const noProviderResult = await validateOAuth2Connection(noProviderReq, undefined);
    console.log('📊 Missing Provider Result:');
    console.log(`   Success: ${noProviderResult.success ? 'Yes ✅' : 'No ❌'}`);
    console.log(`   Error: ${noProviderResult.error}`);
    console.log('');
    
    // Test with missing authorization header
    console.log('🔄 Testing with missing authorization header...');
    const noAuthReq = createMockRequest({});
    
    // Mock OAuth2 provider
    const mockProvider = {
      verifyAccessToken: async (token) => {
        if (token === 'valid-token') {
          return {
            userInfo: {
              sub: 'test-user',
              azp: 'permission-service',
              exp: Math.floor(Date.now() / 1000) + 3600
            },
            scopes: ['openid', 'profile', 'email'],
            clientId: 'permission-service'
          };
        }
        return null;
      }
    };
    
    const noAuthResult = await validateOAuth2Connection(noAuthReq, mockProvider);
    console.log('📊 Missing Authorization Result:');
    console.log(`   Success: ${noAuthResult.success ? 'Yes ✅' : 'No ❌'}`);
    console.log(`   Error: ${noAuthResult.error}`);
    console.log('');
    
    // Test with valid token
    console.log('🔄 Testing with valid OAuth2 token...');
    const validTokenReq = createMockRequest({
      authorization: 'Bearer valid-token'
    });
    
    const validTokenResult = await validateOAuth2Connection(validTokenReq, mockProvider);
    console.log('📊 Valid Token Result:');
    console.log(`   Success: ${validTokenResult.success ? 'Yes ✅' : 'No ❌'}`);
    if (validTokenResult.success) {
      console.log(`   Client ID: ${validTokenResult.clientId}`);
      console.log(`   Scopes: ${validTokenResult.scopes?.join(', ')}`);
      console.log(`   Subject: ${validTokenResult.userInfo?.sub}`);
    } else {
      console.log(`   Error: ${validTokenResult.error}`);
    }
    console.log('');
    
    // Test with invalid token
    console.log('🔄 Testing with invalid OAuth2 token...');
    const invalidTokenReq = createMockRequest({
      authorization: 'Bearer invalid-token'
    });
    
    const invalidTokenResult = await validateOAuth2Connection(invalidTokenReq, mockProvider);
    console.log('📊 Invalid Token Result:');
    console.log(`   Success: ${invalidTokenResult.success ? 'Yes ✅' : 'No ❌'}`);
    console.log(`   Error: ${invalidTokenResult.error}`);
    console.log('');
    
    return {
      noProvider: !noProviderResult.success,
      noAuth: !noAuthResult.success,
      validToken: validTokenResult.success,
      invalidToken: !invalidTokenResult.success
    };
    
  } catch (error) {
    console.error('❌ OAuth2 authentication test failed:', error);
    return {
      noProvider: false,
      noAuth: false,
      validToken: false,
      invalidToken: false
    };
  }
}

/**
 * Test API connection validation
 */
async function testApiConnectionValidation() {
  console.log('🧪 ==================== API CONNECTION TEST ====================');
  
  try {
    // Test with valid token (from environment)
    console.log('🔄 Testing API connection with environment token...');
    const envToken = 'OTJmNjk3MGItZGY5My00MzM3LWE4YjItMTZiZTczYjViZTc5QDE3NTM0MjYwOTAzNjIuM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=';
    
    const apiResult = await testApiConnection(envToken);
    console.log('📊 API Connection Result:');
    console.log(`   Success: ${apiResult.success ? 'Yes ✅' : 'No ❌'}`);
    if (!apiResult.success) {
      console.log(`   Error: ${apiResult.error}`);
    }
    console.log('');
    
    return {
      apiConnection: apiResult.success
    };
    
  } catch (error) {
    console.error('❌ API connection test failed:', error);
    return {
      apiConnection: false
    };
  }
}

/**
 * Test complete connection validation
 */
async function testCompleteConnectionValidation() {
  console.log('🚀 ==================== COMPLETE CONNECTION VALIDATION TEST ====================');
  
  try {
    // Test with valid cookie
    console.log('🔄 Testing complete validation with cookie...');
    const cookieReq = createMockRequest({
      cookie: 'test_orders-portal=OTJmNjk3MGItZGY5My00MzM3LWE4YjItMTZiZTczYjViZTc5QDE3NTM0MjYwOTAzNjIuM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA='
    });
    
    const cookieResult = await validateConnection(cookieReq);
    console.log('📊 Cookie Validation Result:');
    console.log(`   Success: ${cookieResult.success ? 'Yes ✅' : 'No ❌'}`);
    if (cookieResult.success) {
      console.log(`   Client ID: ${cookieResult.clientId}`);
      console.log(`   Method: Cookie`);
    } else {
      console.log(`   Error: ${cookieResult.error}`);
    }
    console.log('');
    
    // Test with no authentication
    console.log('🔄 Testing complete validation with no auth...');
    const noAuthReq = createMockRequest({});
    
    const noAuthResult = await validateConnection(noAuthReq);
    console.log('📊 No Auth Validation Result:');
    console.log(`   Success: ${noAuthResult.success ? 'Yes ✅' : 'No ❌'}`);
    console.log(`   Error: ${noAuthResult.error}`);
    console.log('');
    
    return {
      cookieValidation: cookieResult.success,
      noAuthValidation: !noAuthResult.success
    };
    
  } catch (error) {
    console.error('❌ Complete connection validation test failed:', error);
    return {
      cookieValidation: false,
      noAuthValidation: false
    };
  }
}

/**
 * Show usage instructions
 */
function showUsageInstructions(results) {
  console.log('🔧 ==================== USAGE INSTRUCTIONS ====================');
  
  const config = loadConnectionAuthConfig();
  
  if (config.enabled) {
    console.log('✅ **Connection authentication is enabled**');
    console.log('');
    console.log('📋 **How it works:**');
    console.log('   • Authentication is checked when MCP clients connect');
    console.log('   • Supports both OAuth2 Bearer tokens and authentication cookies');
    console.log('   • Failed authentication blocks MCP connection establishment');
    console.log('');
    console.log('📋 **Configuration options:**');
    console.log(`   CONNECTION_AUTH_ENABLED=${config.enabled} - Enable/disable connection auth`);
    console.log(`   CONNECTION_AUTH_STRICT=${config.strict} - Require OAuth2, no cookie fallback`);
    console.log(`   CONNECTION_AUTH_TEST_API=${config.testApi} - Test API calls during auth`);
    console.log('');
    console.log('📋 **For MCP Inspector:**');
    console.log('   • Use OAuth2 authentication with valid Bearer token');
    console.log('   • Or disable connection auth temporarily: CONNECTION_AUTH_ENABLED=false');
    console.log('');
    console.log('📋 **For Production:**');
    console.log('   • Enable strict mode: CONNECTION_AUTH_STRICT=true');
    console.log('   • Enable API testing: CONNECTION_AUTH_TEST_API=true');
    console.log('   • Use OAuth2 with permission-service client credentials');
    
  } else {
    console.log('⚠️ **Connection authentication is disabled**');
    console.log('');
    console.log('📋 **To enable:**');
    console.log('   Set CONNECTION_AUTH_ENABLED=true in your .env file');
    console.log('');
    console.log('📋 **Benefits of enabling:**');
    console.log('   • Validates authentication before MCP operations');
    console.log('   • Prevents unauthorized access to MCP tools');
    console.log('   • Supports both OAuth2 and cookie authentication');
    console.log('   • Can test API connectivity during connection');
  }
  
  console.log('');
  console.log('⏰ **Important Notes:**');
  console.log('   • Connection auth runs once per MCP session');
  console.log('   • Individual tool calls may have additional auth checks');
  console.log('   • Failed connection auth returns HTTP 401 with details');
  console.log('   • OAuth2 tokens and cookies both have expiration times');
}

/**
 * Main test function
 */
async function runConnectionAuthTest() {
  console.log('🔐 ==================== CONNECTION AUTHENTICATION TEST ====================');
  console.log('🎯 Purpose: Test connection-time authentication validation\n');
  
  // Step 1: Test configuration
  const config = testConnectionAuthConfig();
  
  // Step 2: Test cookie authentication
  const cookieResults = await testCookieAuthentication();
  
  // Step 3: Test OAuth2 authentication
  const oauth2Results = await testOAuth2Authentication();
  
  // Step 4: Test API connection
  const apiResults = await testApiConnectionValidation();
  
  // Step 5: Test complete validation
  const completeResults = await testCompleteConnectionValidation();
  
  // Step 6: Show usage instructions
  showUsageInstructions({
    config,
    ...cookieResults,
    ...oauth2Results,
    ...apiResults,
    ...completeResults
  });
  
  console.log('\n🏁 ==================== TEST COMPLETE ====================');
  console.log('✅ Connection authentication test finished');
  console.log('');
  console.log('🎯 **Final Results:**');
  console.log(`   Configuration: ${config.enabled ? 'Enabled ✅' : 'Disabled ⚠️'}`);
  console.log(`   Cookie Auth: ${cookieResults.validCookie ? 'Working ✅' : 'Failed ❌'}`);
  console.log(`   OAuth2 Auth: ${oauth2Results.validToken ? 'Working ✅' : 'Failed ❌'}`);
  console.log(`   API Testing: ${apiResults.apiConnection ? 'Working ✅' : 'Failed ❌'}`);
  console.log(`   Complete Flow: ${completeResults.cookieValidation ? 'Working ✅' : 'Failed ❌'}`);
  console.log('');
  
  if (config.enabled && (cookieResults.validCookie || oauth2Results.validToken)) {
    console.log('🎉 **Connection authentication is working!**');
    console.log('   MCP connections will be validated for proper authentication');
  } else if (!config.enabled) {
    console.log('⚠️ **Connection authentication is disabled**');
    console.log('   Enable it for enhanced security');
  } else {
    console.log('❌ **Connection authentication needs attention**');
    console.log('   Check authentication tokens and configuration');
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runConnectionAuthTest().catch(error => {
    console.error('💥 Connection authentication test failed:', error);
    process.exit(1);
  });
}

export { runConnectionAuthTest };
