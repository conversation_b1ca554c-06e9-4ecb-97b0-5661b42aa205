{"name": "orders-portal-mcp-server", "version": "0.1.0", "type": "module", "bin": {"orders-portal-mcp-server": "dist/index.js"}, "scripts": {"build": "tsc && shx chmod +x dist/index.js", "watch": "tsc --watch", "start": "node ./dist/index.js", "dev": "tsx watch src/index.ts", "test": "npm run build && npm run start", "test:quick": "npm run build && node test/quick-test.js", "test:auth": "npm run build && node test/auth-cookies.js", "test:tools": "npm run build && node test/all-tools.js", "test:jwt-to-pm-token": "npm run build && node test/jwt-to-pm-token.js", "test:oauth-flow": "npm run build && node test/oauth-flow-mimic.js", "test:permission-exchange": "npm run build && node test/permission-service-exchange.js", "test:permission-auth": "npm run build && node test/permission-service-auth.js", "test:extract-token": "npm run build && node test/extract-browser-token.js", "test:oauth2-mimic": "npm run build && node test/oauth2-flow-mimic.js", "test:direct-auth": "npm run build && node test/direct-backend-auth.js", "test:token-exchange-config": "npm run build && node test/test-token-exchange-config.js", "test:complete-auth": "npm run build && node test/complete-auth-solution.js", "test:token-to-pm": "npm run build && node test/token-to-pm-solution.js", "test:production-auth": "npm run build && node test/production-auth-test.js", "test:auth-flow": "node --import tsx/esm test/auth-flow-test.js", "test:token-forwarding": "node --import tsx/esm test/token-forwarding-test.js", "test:dynamic-cookies": "npm run build && node test/dynamic-cookies.js", "test:jwt-cookies": "npm run build && node test/jwt-cookies.js", "test:pm-token": "npm run build && node test/pm-token.js", "test:token-exchange": "npm run build && node test/token-exchange-methods.js", "test:automated-cookie": "npm run build && node test/automated-cookie-test.js", "test:extract-keycloak-cookies": "npm run build && node test/extract-keycloak-cookies.js", "debug:token": "node --import tsx/esm test/debug-token-introspection.js", "inspect": "npx @modelcontextprotocol/inspector tsx src/index.ts"}, "files": ["dist"], "keywords": ["mcp"], "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT", "description": "orders-portal-mcp-server", "dependencies": {"@modelcontextprotocol/sdk": "^1.16.0", "@types/node-jose": "^1.1.13", "@types/uuid": "^10.0.0", "axios": "^1.6.0", "dotenv": "^16.3.0", "eventsource": "^2.0.2", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "jsonwebtoken": "^9.0.2", "jwks-client": "^2.0.5", "node-jose": "^2.2.0", "uuid": "^11.1.0", "ws": "^8.18.0", "zod": "^3.25.76", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@modelcontextprotocol/inspector": "^0.16.1", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.0.0", "shx": "^0.3.4", "tsx": "^4.0.0", "typescript": "^5.0.0"}}