{"name": "orders-portal-mcp-server", "version": "0.1.0", "type": "module", "bin": {"orders-portal-mcp-server": "dist/index.js"}, "scripts": {"build": "tsc && shx chmod +x dist/index.js", "watch": "tsc --watch", "start": "node ./dist/index.js", "dev": "tsx watch src/index.ts", "test": "npm run build && node test/test.js", "test:legacy": "npm run build && node test/all-tools.js", "jwt-to-cookie": "node scripts/jwt-to-cookie.js", "jwt-to-cookie-simple": "node scripts/jwt-to-cookie-simple.js", "jwt-to-cookie-working": "node scripts/jwt-to-cookie-working.js", "jwt-to-cookie-fixed": "node scripts/jwt-to-cookie-fixed.js", "jwt-to-cookie-real-api": "node scripts/jwt-to-cookie-real-api.js", "jwt-to-cookie-from-401": "node scripts/jwt-to-cookie-from-401.js", "test-manual-cookie": "node scripts/test-manual-cookie.js", "test-jwt-integration": "node scripts/auth-integration.js test", "inspect": "npx @modelcontextprotocol/inspector tsx src/index.ts"}, "files": ["dist"], "keywords": ["mcp"], "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT", "description": "orders-portal-mcp-server", "dependencies": {"@modelcontextprotocol/sdk": "^1.16.0", "@types/node-jose": "^1.1.13", "axios": "^1.6.0", "dotenv": "^16.3.0", "eventsource": "^2.0.2", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "ws": "^8.18.0", "zod": "^3.25.76", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@modelcontextprotocol/inspector": "^0.16.1", "@types/express": "^5.0.1", "@types/node": "^20.0.0", "shx": "^0.3.4", "tsx": "^4.0.0", "typescript": "^5.0.0"}}