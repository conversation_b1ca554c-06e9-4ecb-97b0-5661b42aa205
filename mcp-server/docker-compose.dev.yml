# 🛠️ Development Docker Compose Override
# Configuration for local development with hot reload

version: '3.8'

services:
  mcp-server:
    # Build from local source for development
    build:
      context: .
      dockerfile: Dockerfile
      target: builder  # Use builder stage for development tools
    
    # Development environment overrides
    environment:
      NODE_ENV: development
      
      # Enable debug features
      DEBUG_SERVICE_ADAPTER: true
      
      # Relaxed authentication for development
      CONNECTION_AUTH_ENABLED: false
      CONNECTION_AUTH_STRICT: false
      CONNECTION_AUTH_TEST_API: true
      
      # OAuth2 can be disabled for local development
      OAUTH2_ENABLED: ${OAUTH2_ENABLED:-false}
    
    # Volume mounts for hot reload
    volumes:
      - ./src:/app/src:ro
      - ./package.json:/app/package.json:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
      - ./logs:/app/logs:rw
      - /app/node_modules  # Anonymous volume to prevent host node_modules override
    
    # Development command with hot reload
    command: ["npm", "run", "dev"]
    
    # Development resource limits (more relaxed)
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.1'
          memory: 64M
    
    # Faster health check for development
    healthcheck:
      test: ["CMD", "node", "-e", "fetch('http://localhost:${MCP_SERVER_PORT:-3000}/health').then(r=>r.ok?process.exit(0):process.exit(1)).catch(()=>process.exit(1))"]
      interval: 10s
      timeout: 5s
      retries: 2
      start_period: 10s
    
    # Development logging (more verbose)
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "2"
    
    # Additional development services can be added here
    depends_on:
      - mcp-dev-tools

  # Development tools container (optional)
  mcp-dev-tools:
    image: node:20-alpine
    container_name: mcp-dev-tools
    working_dir: /app
    volumes:
      - .:/app:rw
    command: ["tail", "-f", "/dev/null"]  # Keep container running
    networks:
      - mcp-network

# Development network
networks:
  mcp-network:
    name: orders-portal-mcp-development
